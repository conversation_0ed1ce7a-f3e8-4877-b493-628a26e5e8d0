# ADAMS - Accreditation Document Administration & Management System

A comprehensive web-based system for managing accreditation documents, schedules, and processes for academic institutions. Built with Bootstrap 5, HTML5, CSS3, and JavaScript.

## 🎨 Design Theme

- **Color Scheme**: Notre Dame green theme (`#1e5631`, `#2d7a3f`)
- **UI Framework**: Bootstrap 5 with custom green styling
- **Icons**: Font Awesome 6
- **Typography**: Segoe UI, modern and professional

## 📋 Features

### 1. Dashboard
- **Overview Statistics**: Total programs, upcoming visits, pending documents
- **Quick Actions**: Fast access to common tasks
- **Recent Activity Feed**: Real-time updates on system activities
- **Upcoming Deadlines**: Critical deadline tracking

### 2. Management Modules

#### Departments & Programs
- Manage academic departments (CITE, CN, CCJE, CAS, CED, CBA)
- Program management (BSIT, BSIS, BSCS, BSCpE, BSECE, BSN, BSED, etc.)
- Full CRUD operations with validation
- Export functionality

#### Accrediting Agencies
- **Agencies**: PAASCU, AACCUP, PACUCOA management
- **Levels**: Level I-IV with descriptions and requirements
- **Cycles**: Initial, Revisit, Interim tracking

#### Accreditation Schedules
- Create and manage accreditation timelines
- Map schedules to specific programs
- Track visit dates and coverage periods
- Deadline monitoring

### 3. Document Management
- **File Upload**: Drag & drop interface with file validation
- **Categorization**: 
  - Institutional Documents
  - Program-Specific Documents
  - Shared Documents
- **Area Mapping**: Map documents to PAASCU areas (1-10) and sub-areas
- **Program Linking**: Associate documents with specific programs
- **Status Tracking**: Draft, Under Review, Approved, Rejected
- **Search & Filtering**: Advanced filtering by category, area, status
- **Document Details**: Comprehensive document information display

### 4. User Management
- Role-based access control
- User roles: QA Officer, Area Chair, Program Head, Admin
- Department assignments
- Permission management
- Status tracking (Active/Inactive)

## 🏗️ File Structure

```
ADAMS/
├── index.html          # Dashboard homepage
├── departments.html    # Departments & Programs management
├── documents.html      # Document upload and management
├── schedules.html      # Accreditation schedules
├── agencies.html       # Agencies, levels, and cycles
├── users.html         # User management
├── styles.css         # Custom styling with Notre Dame green theme
├── script.js          # Core JavaScript functionality
└── README.md          # Documentation
```

## 🎯 Key Functional Areas

### Document Categorization Process
1. **Upload**: Drag & drop or browse files
2. **Categorize**: Select Institutional, Program-Specific, or Shared
3. **Map**: Assign to PAASCU areas and sub-areas
4. **Link**: Connect to relevant programs
5. **Review**: Track approval workflow

### Accreditation Area Mapping
- **Area 1**: Philosophy/Objectives
- **Area 2**: Faculty
- **Area 3**: Curriculum & Instruction
- **Area 4**: Support to Students
- **Area 5**: Research
- **Area 6**: Extension & Community Involvement
- **Area 7**: Library
- **Area 8**: Physical Plant & Facilities
- **Area 9**: Laboratories
- **Area 10**: Administration

### Data Management
- All data stored in JavaScript arrays (simulating database)
- Full CRUD operations for all entities
- Data validation and error handling
- Export functionality to CSV format

## 🚀 Getting Started

1. **Open the System**: Open `index.html` in a web browser
2. **Navigate**: Use the top navigation menu to access different modules
3. **Add Data**: Start by adding departments and programs
4. **Upload Documents**: Use the document management module to upload and categorize files
5. **Create Schedules**: Set up accreditation visit schedules
6. **Manage Users**: Add users with appropriate roles and permissions

## 💻 Technical Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Interactive UI**: Modern Bootstrap 5 components with custom styling
- **Form Validation**: Client-side validation with error messages
- **Search & Filter**: Advanced search and filtering capabilities
- **Modal Dialogs**: Clean modal interfaces for data entry
- **Toast Notifications**: User feedback system
- **File Handling**: Drag & drop file upload with validation
- **Export Functionality**: CSV export for all data tables

## 🎨 UI Components

- **Cards**: Statistics and information display
- **Tables**: Data listing with hover effects
- **Modals**: Form entry and detail viewing
- **Badges**: Status indicators with color coding
- **Progress Indicators**: Visual feedback for processes
- **Tooltips**: Contextual help information

## 📱 Responsive Features

- Mobile-friendly navigation with collapsible menu
- Responsive tables with horizontal scrolling
- Optimized touch interfaces
- Adaptive layouts for different screen sizes

## 🔧 Customization

The system uses CSS custom properties for easy theme customization:

```css
:root {
    --nd-green-primary: #1e5631;
    --nd-green-light: #2d7a3f;
    --nd-green-dark: #154223;
    --nd-green-accent: #3a8b4f;
}
```

## 🚀 Future Enhancements

- Database integration (MySQL, PostgreSQL)
- User authentication and session management
- Real-time notifications
- Advanced reporting and analytics
- Document version control
- Audit trail functionality
- Email notifications for deadlines
- Calendar integration

## 📄 License

This project is designed for educational and institutional use. Please ensure compliance with your institution's policies and regulations.

---

**Built for Notre Dame Educational Institutions**  
*Making a Positive Difference in Accreditation Management* 