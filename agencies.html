<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accrediting Agencies - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="true">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse show" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link active" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-building me-3"></i>Accrediting Agencies</h1>
                    <p>Manage accrediting organizations and their standards (PAASCU, AACCUP, PACUCOA)</p>
                </div>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="agencies-tab" data-bs-toggle="tab" data-bs-target="#agencies" type="button" role="tab">
                        <i class="fas fa-building me-2"></i>Agencies
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="levels-tab" data-bs-toggle="tab" data-bs-target="#levels" type="button" role="tab">
                        <i class="fas fa-chart-line me-2"></i>Levels
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cycles-tab" data-bs-toggle="tab" data-bs-target="#cycles" type="button" role="tab">
                        <i class="fas fa-sync me-2"></i>Cycles
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Agencies Tab -->
                <div class="tab-pane fade show active" id="agencies" role="tabpanel">
                    <!-- Search and Filter Bar -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchAgencies" placeholder="Search agencies..." onkeyup="searchTable(this.value, 'agenciesTableBody')">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterAgencyStatus" onchange="filterByStatus(this.value, 'agenciesTableBody')">
                                    <option value="all">All Status</option>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-nd-green" onclick="openAddAgencyModal()">
                                    <i class="fas fa-plus me-2"></i>Add Agency
                                </button>
                                <button class="btn btn-outline-secondary" onclick="exportAgencies()">
                                    <i class="fas fa-download me-2"></i>Export
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Agencies Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-building me-2"></i>Accrediting Agencies</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Acronym</th>
                                            <th>Full Name</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="agenciesTableBody">
                                        <!-- Data populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Levels Tab -->
                <div class="tab-pane fade" id="levels" role="tabpanel">
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-9">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchLevels" placeholder="Search levels..." onkeyup="searchTable(this.value, 'levelsTableBody')">
                                </div>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-nd-green" onclick="openAddLevelModal()">
                                    <i class="fas fa-plus me-2"></i>Add Level
                                </button>
                                <button class="btn btn-outline-secondary" onclick="exportLevels()">
                                    <i class="fas fa-download me-2"></i>Export
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Level</th>
                                            <th>Description</th>
                                            <th>Requirements</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="levelsTableBody">
                                        <!-- Data populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cycles Tab -->
                <div class="tab-pane fade" id="cycles" role="tabpanel">
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-9">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchCycles" placeholder="Search cycles..." onkeyup="searchTable(this.value, 'cyclesTableBody')">
                                </div>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-nd-green" onclick="openAddCycleModal()">
                                    <i class="fas fa-plus me-2"></i>Add Cycle
                                </button>
                                <button class="btn btn-outline-secondary" onclick="exportCycles()">
                                    <i class="fas fa-download me-2"></i>Export
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-sync me-2"></i>Accreditation Cycles</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cyclesTableBody">
                                        <!-- Data populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Agency Modal -->
    <div class="modal fade" id="addAgencyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add Agency</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addAgencyForm">
                        <div class="mb-3">
                            <label for="agencyName" class="form-label">Acronym <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="agencyName" required>
                        </div>
                        <div class="mb-3">
                            <label for="agencyFullName" class="form-label">Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="agencyFullName" required>
                        </div>
                        <div class="mb-3">
                            <label for="agencyStatus" class="form-label">Status</label>
                            <select class="form-select" id="agencyStatus">
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveAgency()">
                        <i class="fas fa-save me-2"></i>Save Agency
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Edit Agency Modal -->
    <div class="modal fade" id="editAgencyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Agency</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editAgencyForm">
                        <div class="mb-3">
                            <label for="editAgencyName" class="form-label">Acronym <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editAgencyName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editAgencyFullName" class="form-label">Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editAgencyFullName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editAgencyStatus" class="form-label">Status</label>
                            <select class="form-select" id="editAgencyStatus">
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateAgency()">
                        <i class="fas fa-save me-2"></i>Update Agency
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Level Modal -->
    <div class="modal fade" id="addLevelModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add Level</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addLevelForm">
                        <div class="mb-3">
                            <label for="levelName" class="form-label">Level <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="levelName" required>
                        </div>
                        <div class="mb-3">
                            <label for="levelDescription" class="form-label">Description <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="levelDescription" required>
                        </div>
                        <div class="mb-3">
                            <label for="levelRequirements" class="form-label">Requirements <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="levelRequirements" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveLevel()">
                        <i class="fas fa-save me-2"></i>Save Level
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Edit Level Modal -->
    <div class="modal fade" id="editLevelModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Level</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editLevelForm">
                        <div class="mb-3">
                            <label for="editLevelName" class="form-label">Level <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editLevelName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editLevelDescription" class="form-label">Description <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editLevelDescription" required>
                        </div>
                        <div class="mb-3">
                            <label for="editLevelRequirements" class="form-label">Requirements <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="editLevelRequirements" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateLevel()">
                        <i class="fas fa-save me-2"></i>Update Level
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Cycle Modal -->
    <div class="modal fade" id="addCycleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add Cycle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCycleForm">
                        <div class="mb-3">
                            <label for="cycleType" class="form-label">Type <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="cycleType" required>
                        </div>
                        <div class="mb-3">
                            <label for="cycleDescription" class="form-label">Description <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="cycleDescription" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveCycle()">
                        <i class="fas fa-save me-2"></i>Save Cycle
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Cycle Modal -->
    <div class="modal fade" id="editCycleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Cycle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editCycleForm">
                        <div class="mb-3">
                            <label for="editCycleType" class="form-label">Type <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editCycleType" required>
                        </div>
                        <div class="mb-3">
                            <label for="editCycleDescription" class="form-label">Description <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editCycleDescription" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateCycle()">
                        <i class="fas fa-save me-2"></i>Update Cycle
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Page-specific variables
        let currentEditingAgencyId = null;
        let currentEditingLevelId = null;
        let currentEditingCycleId = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadAgencies();
            loadLevels();
            loadCycles();
        });

        function loadAgencies() {
            const tableBody = document.getElementById('agenciesTableBody');
            tableBody.innerHTML = '';

            agencies.forEach(agency => {
                const row = `
                    <tr>
                        <td>${agency.id}</td>
                        <td><strong>${agency.name}</strong></td>
                        <td>${agency.fullName}</td>
                        <td class="status-cell">
                            <span class="badge ${agency.status === 'Active' ? 'bg-success' : 'bg-danger'}">${agency.status}</span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-primary" onclick="editAgency(${agency.id})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteAgency(${agency.id})">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        function loadLevels() {
            const tableBody = document.getElementById('levelsTableBody');
            tableBody.innerHTML = '';

            levels.forEach(level => {
                const row = `
                    <tr>
                        <td>${level.id}</td>
                        <td><strong>${level.level}</strong></td>
                        <td>${level.description}</td>
                        <td>${level.requirements}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-primary" onclick="editLevel(${level.id})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteLevel(${level.id})">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        function loadCycles() {
            const tableBody = document.getElementById('cyclesTableBody');
            tableBody.innerHTML = '';

            cycles.forEach(cycle => {
                const row = `
                    <tr>
                        <td>${cycle.id}</td>
                        <td><strong>${cycle.type}</strong></td>
                        <td>${cycle.description}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-primary" onclick="editCycle(${cycle.id})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteCycle(${cycle.id})">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        function openAddAgencyModal() {
            resetForm('addAgencyForm');
            openModal('addAgencyModal');
        }

        function openAddLevelModal() {
            resetForm('addLevelForm');
            openModal('addLevelModal');
        }

        function openAddCycleModal() {
            resetForm('addCycleForm');
            openModal('addCycleModal');
        }

        function saveAgency() {
            if (!validateForm('addAgencyForm')) return;

            const newAgency = {
                id: generateId(agencies),
                name: document.getElementById('agencyName').value,
                fullName: document.getElementById('agencyFullName').value,
                status: document.getElementById('agencyStatus').value
            };

            agencies.push(newAgency);
            loadAgencies();
            closeModal('addAgencyModal');
            showToast('Agency added successfully!');
        }

        function editAgency(id) {
            const agency = getEntityById(agencies, id);
            if (agency) {
                currentEditingAgencyId = id;
                document.getElementById('editAgencyName').value = agency.name;
                document.getElementById('editAgencyFullName').value = agency.fullName;
                document.getElementById('editAgencyStatus').value = agency.status;
                openModal('editAgencyModal');
            }
        }

        function updateAgency() {
            if (!validateForm('editAgencyForm')) return;

            const agencyIndex = agencies.findIndex(a => a.id === currentEditingAgencyId);
            if (agencyIndex !== -1) {
                agencies[agencyIndex] = {
                    ...agencies[agencyIndex],
                    name: document.getElementById('editAgencyName').value,
                    fullName: document.getElementById('editAgencyFullName').value,
                    status: document.getElementById('editAgencyStatus').value
                };

                loadAgencies();
                closeModal('editAgencyModal');
                showToast('Agency updated successfully!');
            }
        }

        function deleteAgency(id) {
            const agency = getEntityById(agencies, id);
            if (confirm(`Are you sure you want to delete "${agency.name}"?`)) {
                agencies = agencies.filter(a => a.id !== id);
                loadAgencies();
                showToast('Agency deleted successfully!');
            }
        }

        function saveLevel() {
            if (!validateForm('addLevelForm')) return;

            const newLevel = {
                id: generateId(levels),
                level: document.getElementById('levelName').value,
                description: document.getElementById('levelDescription').value,
                requirements: document.getElementById('levelRequirements').value
            };

            levels.push(newLevel);
            loadLevels();
            closeModal('addLevelModal');
            showToast('Level added successfully!');
        }

        function editLevel(id) {
            const level = getEntityById(levels, id);
            if (level) {
                currentEditingLevelId = id;
                document.getElementById('editLevelName').value = level.level;
                document.getElementById('editLevelDescription').value = level.description;
                document.getElementById('editLevelRequirements').value = level.requirements;
                openModal('editLevelModal');
            }
        }

        function updateLevel() {
            if (!validateForm('editLevelForm')) return;

            const levelIndex = levels.findIndex(l => l.id === currentEditingLevelId);
            if (levelIndex !== -1) {
                levels[levelIndex] = {
                    ...levels[levelIndex],
                    level: document.getElementById('editLevelName').value,
                    description: document.getElementById('editLevelDescription').value,
                    requirements: document.getElementById('editLevelRequirements').value
                };

                loadLevels();
                closeModal('editLevelModal');
                showToast('Level updated successfully!');
            }
        }

        function deleteLevel(id) {
            const level = getEntityById(levels, id);
            if (confirm(`Are you sure you want to delete "${level.level}"?`)) {
                levels = levels.filter(l => l.id !== id);
                loadLevels();
                showToast('Level deleted successfully!');
            }
        }

        function saveCycle() {
            if (!validateForm('addCycleForm')) return;

            const newCycle = {
                id: generateId(cycles),
                type: document.getElementById('cycleType').value,
                description: document.getElementById('cycleDescription').value
            };

            cycles.push(newCycle);
            loadCycles();
            closeModal('addCycleModal');
            showToast('Cycle added successfully!');
        }

        function editCycle(id) {
            const cycle = getEntityById(cycles, id);
            if (cycle) {
                currentEditingCycleId = id;
                document.getElementById('editCycleType').value = cycle.type;
                document.getElementById('editCycleDescription').value = cycle.description;
                openModal('editCycleModal');
            }
        }

        function updateCycle() {
            if (!validateForm('editCycleForm')) return;

            const cycleIndex = cycles.findIndex(c => c.id === currentEditingCycleId);
            if (cycleIndex !== -1) {
                cycles[cycleIndex] = {
                    ...cycles[cycleIndex],
                    type: document.getElementById('editCycleType').value,
                    description: document.getElementById('editCycleDescription').value
                };

                loadCycles();
                closeModal('editCycleModal');
                showToast('Cycle updated successfully!');
            }
        }

        function deleteCycle(id) {
            const cycle = getEntityById(cycles, id);
            if (confirm(`Are you sure you want to delete "${cycle.type}"?`)) {
                cycles = cycles.filter(c => c.id !== id);
                loadCycles();
                showToast('Cycle deleted successfully!');
            }
        }

        // Export Functions
        function exportAgencies() {
            exportToCSV(agencies, 'accrediting_agencies.csv');
        }

        function exportLevels() {
            exportToCSV(levels, 'accreditation_levels.csv');
        }

        function exportCycles() {
            exportToCSV(cycles, 'accreditation_cycles.csv');
        }
    </script>
</body>
</html> 