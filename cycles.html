<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accreditation Cycles - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-sync me-3"></i>Accreditation Cycles</h1>
                    <p>Manage accreditation cycle types and visit schedules for quality assurance processes</p>
                </div>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="application-stage-tab" data-bs-toggle="tab" data-bs-target="#application-stage" type="button" role="tab">
                        <i class="fas fa-file-alt me-2"></i>Application Stage
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="self-survey-stage-tab" data-bs-toggle="tab" data-bs-target="#self-survey-stage" type="button" role="tab">
                        <i class="fas fa-clipboard-check me-2"></i>Self-Survey Stage
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="pre-visit-stage-tab" data-bs-toggle="tab" data-bs-target="#pre-visit-stage" type="button" role="tab">
                        <i class="fas fa-tasks me-2"></i>Pre-Visit Stage
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="formal-visit-stage-tab" data-bs-toggle="tab" data-bs-target="#formal-visit-stage" type="button" role="tab">
                        <i class="fas fa-users me-2"></i>Formal Visit Stage
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="post-accreditation-tab" data-bs-toggle="tab" data-bs-target="#post-accreditation" type="button" role="tab">
                        <i class="fas fa-award me-2"></i>Post-Accreditation
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Application Stage Tab -->
                <div class="tab-pane fade show active" id="application-stage" role="tabpanel">
                    <!-- Application Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="applicationProgram" onchange="filterApplications()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="applicationStatus" onchange="filterApplications()">
                                    <option value="all">All Status</option>
                                    <option value="Planning">Planning</option>
                                    <option value="Documents Preparation">Documents Preparation</option>
                                    <option value="Under Review">Under Review</option>
                                    <option value="Submitted">Submitted</option>
                                    <option value="Approved">Approved</option>
                                    <option value="Rejected">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="applicationYear" onchange="filterApplications()">
                                    <option value="all">All Years</option>
                                    <option value="2024">2024</option>
                                    <option value="2025">2025</option>
                                    <option value="2026">2026</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-primary" onclick="openAddApplicationModal()">
                                    <i class="fas fa-plus me-2"></i>Start Application
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Application Progress Cards -->
                    <div class="row" id="applicationCardsContainer">
                        <!-- Application cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Self-Survey Stage Tab -->
                <div class="tab-pane fade" id="self-survey-stage" role="tabpanel">
                    <!-- Self-Survey Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="surveyStageProgram" onchange="filterSurveyStage()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="surveyStageStatus" onchange="filterSurveyStage()">
                                    <option value="all">All Status</option>
                                    <option value="Not Started">Not Started</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Under Review">Under Review</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Approved">Approved</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="surveyStageArea" onchange="filterSurveyStage()">
                                    <option value="all">All Areas</option>
                                    <option value="Area 1">Area 1: Philosophy/Objectives</option>
                                    <option value="Area 2">Area 2: Faculty</option>
                                    <option value="Area 3">Area 3: Curriculum & Instruction</option>
                                    <option value="Area 4">Area 4: Support to Students</option>
                                    <option value="Area 5">Area 5: Research</option>
                                    <option value="Area 6">Area 6: Extension & Community</option>
                                    <option value="Area 7">Area 7: Library</option>
                                    <option value="Area 8">Area 8: Physical Plant</option>
                                    <option value="Area 9">Area 9: Laboratories</option>
                                    <option value="Area 10">Area 10: Administration</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-success" onclick="openSurveyWorkflowModal()">
                                    <i class="fas fa-plus me-2"></i>Start Survey
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Survey Progress Grid -->
                    <div id="surveyStageContainer">
                        <!-- Survey stage content will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Pre-Visit Stage Tab -->
                <div class="tab-pane fade" id="pre-visit-stage" role="tabpanel">
                    <!-- Pre-Visit Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="preVisitProgram" onchange="filterPreVisit()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="preVisitStatus" onchange="filterPreVisit()">
                                    <option value="all">All Status</option>
                                    <option value="Preparation">Preparation</option>
                                    <option value="Document Review">Document Review</option>
                                    <option value="Team Assignment">Team Assignment</option>
                                    <option value="Schedule Coordination">Schedule Coordination</option>
                                    <option value="Ready for Visit">Ready for Visit</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="preVisitDate" onchange="filterPreVisit()" placeholder="Filter by visit date">
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-warning" onclick="openPreVisitPlanModal()">
                                    <i class="fas fa-plus me-2"></i>Plan Pre-Visit
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Pre-Visit Timeline -->
                    <div id="preVisitContainer">
                        <!-- Pre-visit content will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Formal Visit Stage Tab -->
                <div class="tab-pane fade" id="formal-visit-stage" role="tabpanel">
                    <!-- Formal Visit Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="visitProgram" onchange="filterFormalVisits()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="visitStatus" onchange="filterFormalVisits()">
                                    <option value="all">All Status</option>
                                    <option value="Scheduled">Scheduled</option>
                                    <option value="Ongoing">Ongoing</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Rescheduled">Rescheduled</option>
                                    <option value="Cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="visitEvaluator" onchange="filterFormalVisits()">
                                    <option value="all">All Evaluators</option>
                                    <option value="PAASCU">PAASCU</option>
                                    <option value="AACCUP">AACCUP</option>
                                    <option value="PACUCOA">PACUCOA</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-info" onclick="openVisitManagementModal()">
                                    <i class="fas fa-plus me-2"></i>Schedule Visit
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Formal Visit Cards -->
                    <div class="row" id="formalVisitContainer">
                        <!-- Formal visit cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Post-Accreditation Tab -->
                <div class="tab-pane fade" id="post-accreditation" role="tabpanel">
                    <!-- Post-Accreditation Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="postAccreditationProgram" onchange="filterPostAccreditation()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="postAccreditationStatus" onchange="filterPostAccreditation()">
                                    <option value="all">All Status</option>
                                    <option value="Accredited">Accredited</option>
                                    <option value="Conditional">Conditional</option>
                                    <option value="Re-visit Required">Re-visit Required</option>
                                    <option value="Not Accredited">Not Accredited</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="postAccreditationLevel" onchange="filterPostAccreditation()">
                                    <option value="all">All Levels</option>
                                    <option value="Level I">Level I</option>
                                    <option value="Level II">Level II</option>
                                    <option value="Level III">Level III</option>
                                    <option value="Level IV">Level IV</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-success" onclick="openPostAccreditationModal()">
                                    <i class="fas fa-plus me-2"></i>Record Result
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Post-Accreditation Results -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-award me-2"></i>Accreditation Results & Monitoring</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Accreditation Level</th>
                                            <th>Status</th>
                                            <th>Valid Until</th>
                                            <th>Recommendations</th>
                                            <th>Compliance Status</th>
                                            <th>Next Action</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="postAccreditationTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Cycle Modal -->
    <div class="modal fade" id="addCycleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Cycle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCycleForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Cycle Type <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="cycleType" placeholder="e.g., Initial" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Duration (Years)</label>
                                    <input type="number" class="form-control" id="cycleDuration" placeholder="e.g., 5" min="1" max="10">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="cycleStatus" required>
                                        <option value="">Select Status</option>
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <select class="form-select" id="cyclePriority">
                                        <option value="Normal">Normal</option>
                                        <option value="High">High</option>
                                        <option value="Critical">Critical</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Description <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="cycleDescription" rows="4" placeholder="Describe the purpose and process of this cycle..." required></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveCycle()">
                        <i class="fas fa-save me-2"></i>Save Cycle
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Cycle Modal -->
    <div class="modal fade" id="editCycleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Cycle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editCycleForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Cycle Type <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editCycleType" placeholder="e.g., Initial" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Duration (Years)</label>
                                    <input type="number" class="form-control" id="editCycleDuration" placeholder="e.g., 5" min="1" max="10">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editCycleStatus" required>
                                        <option value="">Select Status</option>
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <select class="form-select" id="editCyclePriority">
                                        <option value="Normal">Normal</option>
                                        <option value="High">High</option>
                                        <option value="Critical">Critical</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Description <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="editCycleDescription" rows="4" placeholder="Describe the purpose and process of this cycle..." required></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateCycle()">
                        <i class="fas fa-save me-2"></i>Update Cycle
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Cycle Details Modal -->
    <div class="modal fade" id="viewCycleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Cycle Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Cycle Type:</label>
                                <p id="viewCycleType" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Duration:</label>
                                <p id="viewCycleDuration" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <p id="viewCycleStatus" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Priority:</label>
                                <p id="viewCyclePriority" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Description:</label>
                                <p id="viewCycleDescription" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Page-specific JavaScript for Cycle Tracker
        let currentEditingCycleId = null;
        let applicationStageData = [];
        let selfSurveyStageData = [];
        let preVisitStageData = [];
        let formalVisitStageData = [];
        let postAccreditationData = [];
        let evaluationResults = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeCycleData();
            loadApplicationStage();
            loadSelfSurveyStage();
            loadPreVisitStage();
            loadFormalVisitStage();
            loadPostAccreditation();
            populateDropdowns();
        });

        // Initialize sample data for all stages
        function initializeCycleData() {
            if (applicationStageData.length === 0) {
                applicationStageData = [
                    { 
                        id: 1, 
                        programId: 1, 
                        status: 'Documents Preparation', 
                        year: '2024', 
                        startDate: '2024-01-15', 
                        targetSubmission: '2024-03-30',
                        progress: 75,
                        checklist: [
                            { item: 'Institutional Requirements', completed: true },
                            { item: 'Program Documents', completed: true },
                            { item: 'Faculty Credentials', completed: true },
                            { item: 'Infrastructure Assessment', completed: false }
                        ]
                    },
                    { 
                        id: 2, 
                        programId: 2, 
                        status: 'Planning', 
                        year: '2024', 
                        startDate: '2024-02-01', 
                        targetSubmission: '2024-05-15',
                        progress: 25,
                        checklist: [
                            { item: 'Institutional Requirements', completed: true },
                            { item: 'Program Documents', completed: false },
                            { item: 'Faculty Credentials', completed: false },
                            { item: 'Infrastructure Assessment', completed: false }
                        ]
                    }
                ];
            }

            if (selfSurveyStageData.length === 0) {
                selfSurveyStageData = [
                    { 
                        id: 1, 
                        programId: 1, 
                        status: 'In Progress', 
                        areas: [
                            { area: 'Area 1', status: 'Completed', progress: 100 },
                            { area: 'Area 2', status: 'In Progress', progress: 65 },
                            { area: 'Area 3', status: 'Not Started', progress: 0 },
                            { area: 'Area 4', status: 'Not Started', progress: 0 }
                        ],
                        overallProgress: 41,
                        startDate: '2024-02-01',
                        deadline: '2024-04-30'
                    },
                    { 
                        id: 2, 
                        programId: 2, 
                        status: 'Not Started', 
                        areas: [
                            { area: 'Area 1', status: 'Not Started', progress: 0 },
                            { area: 'Area 2', status: 'Not Started', progress: 0 },
                            { area: 'Area 3', status: 'Not Started', progress: 0 },
                            { area: 'Area 4', status: 'Not Started', progress: 0 }
                        ],
                        overallProgress: 0,
                        startDate: '2024-03-01',
                        deadline: '2024-06-30'
                    }
                ];
            }

            if (preVisitStageData.length === 0) {
                preVisitStageData = [
                    { 
                        id: 1, 
                        programId: 1, 
                        status: 'Schedule Coordination', 
                        visitDate: '2024-05-15',
                        evaluatorTeam: ['Dr. Smith', 'Dr. Johnson', 'Dr. Williams'],
                        tasks: [
                            { task: 'Document Review', status: 'Completed', dueDate: '2024-04-20' },
                            { task: 'Team Assignment', status: 'Completed', dueDate: '2024-04-25' },
                            { task: 'Schedule Coordination', status: 'In Progress', dueDate: '2024-05-05' },
                            { task: 'Final Preparation', status: 'Pending', dueDate: '2024-05-10' }
                        ]
                    },
                    { 
                        id: 2, 
                        programId: 2, 
                        status: 'Document Review', 
                        visitDate: '2024-07-10',
                        evaluatorTeam: ['Dr. Brown', 'Dr. Davis'],
                        tasks: [
                            { task: 'Document Review', status: 'In Progress', dueDate: '2024-06-15' },
                            { task: 'Team Assignment', status: 'Pending', dueDate: '2024-06-20' },
                            { task: 'Schedule Coordination', status: 'Pending', dueDate: '2024-07-01' },
                            { task: 'Final Preparation', status: 'Pending', dueDate: '2024-07-05' }
                        ]
                    }
                ];
            }

            if (formalVisitStageData.length === 0) {
                formalVisitStageData = [
                    { 
                        id: 1, 
                        programId: 1, 
                        status: 'Scheduled', 
                        visitDate: '2024-05-15',
                        visitDuration: '3 days',
                        evaluator: 'PAASCU',
                        teamLead: 'Dr. Maria Santos',
                        teamMembers: ['Dr. John Smith', 'Dr. Emily Johnson'],
                        visitType: 'Initial Accreditation',
                        schedule: [
                            { day: 'Day 1', activities: ['Opening Ceremony', 'Facility Tour', 'Document Review'] },
                            { day: 'Day 2', activities: ['Faculty Interviews', 'Student Focus Groups', 'Classroom Observations'] },
                            { day: 'Day 3', activities: ['Exit Conference', 'Preliminary Feedback', 'Report Discussion'] }
                        ]
                    },
                    { 
                        id: 2, 
                        programId: 3, 
                        status: 'Completed', 
                        visitDate: '2024-03-20',
                        visitDuration: '2 days',
                        evaluator: 'AACCUP',
                        teamLead: 'Dr. Robert Garcia',
                        teamMembers: ['Dr. Lisa Chen'],
                        visitType: 'Re-accreditation',
                        finalRating: 'Level II',
                        recommendations: 3
                    }
                ];
            }

            if (postAccreditationData.length === 0) {
                postAccreditationData = [
                    { 
                        id: 1, 
                        programId: 1, 
                        accreditationLevel: 'Level III', 
                        status: 'Accredited',
                        validUntil: '2027-05-15',
                        recommendations: 2,
                        complianceStatus: 'Monitoring',
                        nextAction: 'Annual Report Due',
                        nextActionDate: '2025-02-28',
                        certificate: 'CERT-2024-001.pdf'
                    },
                    { 
                        id: 2, 
                        programId: 2, 
                        accreditationLevel: 'Level II', 
                        status: 'Conditional',
                        validUntil: '2025-12-31',
                        recommendations: 5,
                        complianceStatus: 'Action Required',
                        nextAction: 'Submit Compliance Report',
                        nextActionDate: '2024-06-30',
                        certificate: 'CERT-2024-002.pdf'
                    },
                    { 
                        id: 3, 
                        programId: 3, 
                        accreditationLevel: 'Level II', 
                        status: 'Accredited',
                        validUntil: '2026-03-20',
                        recommendations: 3,
                        complianceStatus: 'Compliant',
                        nextAction: 'Mid-term Report',
                        nextActionDate: '2025-03-20',
                        certificate: 'CERT-2024-003.pdf'
                    }
                ];
            }

            if (evaluationResults.length === 0) {
                evaluationResults = [
                    { id: 1, programId: 1, evaluationType: 'Initial Accreditation', evaluator: 'PAASCU', date: '2023-11-15', overallRating: 'Level III', status: 'Final', recommendations: 5 },
                    { id: 2, programId: 2, evaluationType: 'Re-accreditation', evaluator: 'AACCUP', date: '2024-01-20', overallRating: 'Accredited', status: 'Final', recommendations: 3 },
                    { id: 3, programId: 3, evaluationType: 'Follow-up Visit', evaluator: 'PAASCU', date: '2024-02-10', overallRating: 'Satisfactory', status: 'Pending', recommendations: 2 }
                ];
            }
        }

        // Application Stage Functions
        function loadApplicationStage() {
            const container = document.getElementById('applicationCardsContainer');
            container.innerHTML = '';

            applicationStageData.forEach(application => {
                const program = getEntityById(programs, application.programId);
                const completedTasks = application.checklist.filter(item => item.completed).length;
                const totalTasks = application.checklist.length;
                const progressColor = application.progress >= 80 ? 'success' : application.progress >= 50 ? 'warning' : 'danger';
                
                const card = `
                    <div class="col-lg-6 col-md-12 mb-4">
                        <div class="card h-100">
                            <div class="card-header ${getStatusHeaderClass(application.status)} text-white">
                                <h6 class="card-title mb-0">${program ? program.name : 'Unknown Program'}</h6>
                                <small>Application ${application.year}</small>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <p><strong>Status:</strong> <span class="badge ${getStatusBadgeClass(application.status)}">${application.status}</span></p>
                                        <p><strong>Start Date:</strong> ${formatDate(application.startDate)}</p>
                                        <p><strong>Target Submission:</strong> ${formatDate(application.targetSubmission)}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Progress:</strong> ${application.progress}%</p>
                                        <div class="progress mb-2">
                                            <div class="progress-bar bg-${progressColor}" style="width: ${application.progress}%"></div>
                                        </div>
                                        <p><strong>Tasks:</strong> ${completedTasks}/${totalTasks} completed</p>
                                    </div>
                                </div>
                                <div class="checklist">
                                    <h6>Application Checklist:</h6>
                                    ${application.checklist.map(item => `
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" ${item.completed ? 'checked' : ''} disabled>
                                            <label class="form-check-label ${item.completed ? 'text-success' : 'text-muted'}">
                                                ${item.item}
                                            </label>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewApplication(${application.id})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="editApplication(${application.id})">
                                        <i class="fas fa-edit"></i> Update
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="exportApplication(${application.id})">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += card;
            });
        }

        // Self-Survey Stage Functions
        function loadSelfSurveyStage() {
            const container = document.getElementById('surveyStageContainer');
            container.innerHTML = '';

            selfSurveyStageData.forEach(survey => {
                const program = getEntityById(programs, survey.programId);
                const progressColor = survey.overallProgress >= 80 ? 'success' : survey.overallProgress >= 50 ? 'warning' : 'danger';
                
                const content = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header ${getStatusHeaderClass(survey.status)} text-white">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h6 class="mb-0">${program ? program.name : 'Unknown Program'} - Self-Survey</h6>
                                            <small>Overall Progress: ${survey.overallProgress}%</small>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-white" style="width: ${survey.overallProgress}%" title="${survey.overallProgress}% Complete">
                                                    ${survey.overallProgress}%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <p><strong>Start Date:</strong> ${formatDate(survey.startDate)}</p>
                                            <p><strong>Deadline:</strong> ${formatDate(survey.deadline)}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Status:</strong> <span class="badge ${getStatusBadgeClass(survey.status)}">${survey.status}</span></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        ${survey.areas.map(area => `
                                            <div class="col-md-3 mb-3">
                                                <div class="card area-card ${getBorderClass(area.status)}">
                                                    <div class="card-body text-center">
                                                        <h6>${area.area}</h6>
                                                        <div class="progress mb-2">
                                                            <div class="progress-bar ${getProgressColor(area.progress)}" style="width: ${area.progress}%">
                                                                ${area.progress}%
                                                            </div>
                                                        </div>
                                                        <span class="badge ${getStatusBadgeClass(area.status)}">${area.status}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group">
                                        <button class="btn btn-outline-primary btn-sm" onclick="viewSurveyProgress(${survey.id})">
                                            <i class="fas fa-chart-line"></i> View Progress
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" onclick="editSurveyProgress(${survey.id})">
                                            <i class="fas fa-edit"></i> Update
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" onclick="exportSurveyProgress(${survey.id})">
                                            <i class="fas fa-download"></i> Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += content;
            });
        }

        // Pre-Visit Stage Functions
        function loadPreVisitStage() {
            const container = document.getElementById('preVisitContainer');
            container.innerHTML = '';

            preVisitStageData.forEach(preVisit => {
                const program = getEntityById(programs, preVisit.programId);
                const completedTasks = preVisit.tasks.filter(task => task.status === 'Completed').length;
                const totalTasks = preVisit.tasks.length;
                const progressPercentage = Math.round((completedTasks / totalTasks) * 100);
                
                const content = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header ${getStatusHeaderClass(preVisit.status)} text-white">
                                    <h6 class="mb-0">${program ? program.name : 'Unknown Program'} - Pre-Visit Preparation</h6>
                                    <small>Visit Date: ${formatDate(preVisit.visitDate)}</small>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <p><strong>Status:</strong> <span class="badge ${getStatusBadgeClass(preVisit.status)}">${preVisit.status}</span></p>
                                            <p><strong>Evaluator Team:</strong></p>
                                            <ul class="mb-0">
                                                ${preVisit.evaluatorTeam.map(member => `<li>${member}</li>`).join('')}
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Task Completion:</strong> ${completedTasks}/${totalTasks} (${progressPercentage}%)</p>
                                            <div class="progress mb-2">
                                                <div class="progress-bar ${getProgressColor(progressPercentage)}" style="width: ${progressPercentage}%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="timeline">
                                        <h6>Pre-Visit Tasks:</h6>
                                        ${preVisit.tasks.map(task => `
                                            <div class="timeline-item d-flex align-items-center mb-2">
                                                <div class="timeline-marker ${getTaskMarkerClass(task.status)}">
                                                    <i class="fas ${getTaskIcon(task.status)}"></i>
                                                </div>
                                                <div class="timeline-content ms-3">
                                                    <strong>${task.task}</strong>
                                                    <span class="badge ${getStatusBadgeClass(task.status)} ms-2">${task.status}</span>
                                                    <br><small class="text-muted">Due: ${formatDate(task.dueDate)}</small>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group">
                                        <button class="btn btn-outline-primary btn-sm" onclick="viewPreVisitDetails(${preVisit.id})">
                                            <i class="fas fa-eye"></i> View Details
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" onclick="updatePreVisitStatus(${preVisit.id})">
                                            <i class="fas fa-edit"></i> Update
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="rescheduleVisit(${preVisit.id})">
                                            <i class="fas fa-calendar-alt"></i> Reschedule
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += content;
            });
        }

        // Formal Visit Stage Functions
        function loadFormalVisitStage() {
            const container = document.getElementById('formalVisitContainer');
            container.innerHTML = '';

            formalVisitStageData.forEach(visit => {
                const program = getEntityById(programs, visit.programId);
                
                const card = `
                    <div class="col-lg-6 col-md-12 mb-4">
                        <div class="card h-100">
                            <div class="card-header ${getStatusHeaderClass(visit.status)} text-white">
                                <h6 class="card-title mb-0">${program ? program.name : 'Unknown Program'}</h6>
                                <small>${visit.visitType}</small>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <p><strong>Visit Date:</strong> ${formatDate(visit.visitDate)}</p>
                                        <p><strong>Duration:</strong> ${visit.visitDuration}</p>
                                        <p><strong>Evaluator:</strong> ${visit.evaluator}</p>
                                        <p><strong>Status:</strong> <span class="badge ${getStatusBadgeClass(visit.status)}">${visit.status}</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Team Lead:</strong> ${visit.teamLead}</p>
                                        <p><strong>Team Members:</strong></p>
                                        <ul class="small">
                                            ${visit.teamMembers.map(member => `<li>${member}</li>`).join('')}
                                        </ul>
                                    </div>
                                </div>
                                ${visit.status === 'Completed' ? `
                                    <div class="alert alert-success">
                                        <strong>Visit Completed!</strong><br>
                                        Final Rating: <span class="badge bg-success">${visit.finalRating}</span><br>
                                        Recommendations: ${visit.recommendations}
                                    </div>
                                ` : visit.schedule ? `
                                    <div class="visit-schedule">
                                        <h6>Visit Schedule:</h6>
                                        ${visit.schedule.map(day => `
                                            <div class="mb-2">
                                                <strong>${day.day}:</strong>
                                                <ul class="small mb-0">
                                                    ${day.activities.map(activity => `<li>${activity}</li>`).join('')}
                                                </ul>
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : ''}
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewVisitDetails(${visit.id})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="manageVisit(${visit.id})">
                                        <i class="fas fa-cog"></i> Manage
                                    </button>
                                    ${visit.status === 'Completed' ? `
                                        <button class="btn btn-outline-info btn-sm" onclick="viewVisitReport(${visit.id})">
                                            <i class="fas fa-file-alt"></i> Report
                                        </button>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += card;
            });
        }

        // Post-Accreditation Functions
        function loadPostAccreditation() {
            const tbody = document.getElementById('postAccreditationTableBody');
            tbody.innerHTML = '';

            postAccreditationData.forEach(result => {
                const program = getEntityById(programs, result.programId);
                const daysUntilExpiry = Math.ceil((new Date(result.validUntil) - new Date()) / (1000 * 60 * 60 * 24));
                const daysUntilAction = Math.ceil((new Date(result.nextActionDate) - new Date()) / (1000 * 60 * 60 * 24));
                
                const row = `
                    <tr>
                        <td><strong>${program ? program.name : 'Unknown'}</strong></td>
                        <td><span class="badge ${getLevelBadgeClass(result.accreditationLevel)}">${result.accreditationLevel}</span></td>
                        <td><span class="badge ${getStatusBadgeClass(result.status)}">${result.status}</span></td>
                        <td>
                            ${formatDate(result.validUntil)}
                            <br><small class="text-muted">${daysUntilExpiry > 0 ? `${daysUntilExpiry} days remaining` : `Expired ${Math.abs(daysUntilExpiry)} days ago`}</small>
                        </td>
                        <td>
                            <span class="badge bg-warning">${result.recommendations}</span>
                            <button class="btn btn-sm btn-outline-info ms-1" onclick="viewRecommendations(${result.id})">
                                <i class="fas fa-list"></i>
                            </button>
                        </td>
                        <td><span class="badge ${getComplianceBadgeClass(result.complianceStatus)}">${result.complianceStatus}</span></td>
                        <td>
                            ${result.nextAction}
                            <br><small class="text-muted">Due: ${formatDate(result.nextActionDate)} (${daysUntilAction} days)</small>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewAccreditationDetails(${result.id})">
                                <i class="fas fa-eye"></i> View
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="downloadCertificate(${result.id})">
                                <i class="fas fa-certificate"></i> Certificate
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="updateCompliance(${result.id})">
                                <i class="fas fa-edit"></i> Update
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Helper functions
        function getStatusHeaderClass(status) {
            switch(status) {
                case 'Completed': 
                case 'Approved': 
                case 'Accredited': return 'bg-success';
                case 'In Progress': 
                case 'Ongoing': 
                case 'Scheduled': return 'bg-primary';
                case 'Planning': 
                case 'Preparation': 
                case 'Under Review': return 'bg-warning';
                case 'Not Started': 
                case 'Rejected': 
                case 'Cancelled': return 'bg-secondary';
                default: return 'bg-info';
            }
        }

        function getStatusBadgeClass(status) {
            switch(status) {
                case 'Completed': 
                case 'Approved': 
                case 'Accredited': 
                case 'Ready for Visit': return 'bg-success';
                case 'In Progress': 
                case 'Ongoing': 
                case 'Scheduled': 
                case 'Documents Preparation': return 'bg-primary';
                case 'Planning': 
                case 'Preparation': 
                case 'Under Review': 
                case 'Conditional': 
                case 'Document Review': return 'bg-warning';
                case 'Not Started': 
                case 'Rejected': 
                case 'Cancelled': 
                case 'Not Accredited': return 'bg-secondary';
                case 'Submitted': return 'bg-info';
                default: return 'bg-secondary';
            }
        }

        function getBorderClass(status) {
            switch(status) {
                case 'Completed': return 'border-success';
                case 'In Progress': return 'border-warning';
                case 'Not Started': return 'border-secondary';
                default: return 'border-info';
            }
        }

        function getProgressColor(progress) {
            if (progress >= 80) return 'bg-success';
            if (progress >= 50) return 'bg-warning';
            return 'bg-danger';
        }

        function getTaskMarkerClass(status) {
            switch(status) {
                case 'Completed': return 'bg-success text-white';
                case 'In Progress': return 'bg-warning text-white';
                case 'Pending': return 'bg-secondary text-white';
                default: return 'bg-info text-white';
            }
        }

        function getTaskIcon(status) {
            switch(status) {
                case 'Completed': return 'fa-check';
                case 'In Progress': return 'fa-clock';
                case 'Pending': return 'fa-hourglass-half';
                default: return 'fa-circle';
            }
        }

        function getLevelBadgeClass(level) {
            switch(level) {
                case 'Level IV': return 'bg-success';
                case 'Level III': return 'bg-primary';
                case 'Level II': return 'bg-info';
                case 'Level I': return 'bg-warning';
                default: return 'bg-secondary';
            }
        }

        function getComplianceBadgeClass(status) {
            switch(status) {
                case 'Compliant': return 'bg-success';
                case 'Monitoring': return 'bg-info';
                case 'Action Required': return 'bg-warning';
                case 'Non-Compliant': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        // Populate dropdowns
        function populateDropdowns() {
            const programSelects = ['applicationProgram', 'surveyStageProgram', 'preVisitProgram', 'visitProgram', 'postAccreditationProgram'];
            programSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const currentValue = select.value;
                    select.innerHTML = '<option value="all">All Programs</option>';
                    programs.forEach(program => {
                        select.innerHTML += `<option value="${program.id}">${program.name}</option>`;
                    });
                    if (currentValue) select.value = currentValue;
                }
            });
        }

        // Modal functions
        function openAddApplicationModal() { 
            // Create a new application using a simplified approach
            const programId = prompt('Enter Program ID (1-7):');
            if (!programId || isNaN(programId)) {
                showToast('Invalid Program ID', 'error');
                return;
            }

            const program = getEntityById(programs, parseInt(programId));
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const targetDate = prompt('Enter target submission date (YYYY-MM-DD):', '2024-12-31');
            if (!targetDate) return;

            const newApplication = {
                id: generateId(applicationStageData),
                programId: parseInt(programId),
                status: 'Planning',
                year: '2024',
                startDate: new Date().toISOString().split('T')[0],
                targetSubmission: targetDate,
                progress: 0,
                checklist: [
                    { item: 'Institutional Requirements', completed: false },
                    { item: 'Program Documents', completed: false },
                    { item: 'Faculty Credentials', completed: false },
                    { item: 'Infrastructure Assessment', completed: false }
                ]
            };

            applicationStageData.push(newApplication);
            loadApplicationStage();
            showToast('Application created successfully!');
        }

        function openSurveyWorkflowModal() { 
            // Create a new survey workflow
            const programId = prompt('Enter Program ID (1-7):');
            if (!programId || isNaN(programId)) {
                showToast('Invalid Program ID', 'error');
                return;
            }

            const program = getEntityById(programs, parseInt(programId));
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const deadline = prompt('Enter survey deadline (YYYY-MM-DD):', '2024-12-31');
            if (!deadline) return;

            const newSurvey = {
                id: generateId(selfSurveyStageData),
                programId: parseInt(programId),
                status: 'Not Started',
                areas: [
                    { area: 'Area 1', status: 'Not Started', progress: 0 },
                    { area: 'Area 2', status: 'Not Started', progress: 0 },
                    { area: 'Area 3', status: 'Not Started', progress: 0 },
                    { area: 'Area 4', status: 'Not Started', progress: 0 }
                ],
                overallProgress: 0,
                startDate: new Date().toISOString().split('T')[0],
                deadline: deadline
            };

            selfSurveyStageData.push(newSurvey);
            loadSelfSurveyStage();
            showToast('Survey workflow created successfully!');
        }

        function openPreVisitPlanModal() { 
            // Create a new pre-visit plan
            const programId = prompt('Enter Program ID (1-7):');
            if (!programId || isNaN(programId)) {
                showToast('Invalid Program ID', 'error');
                return;
            }

            const program = getEntityById(programs, parseInt(programId));
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const visitDate = prompt('Enter scheduled visit date (YYYY-MM-DD):', '2024-12-31');
            if (!visitDate) return;

            const newPreVisit = {
                id: generateId(preVisitStageData),
                programId: parseInt(programId),
                status: 'Planning',
                visitDate: visitDate,
                evaluatorTeam: ['Dr. Evaluator 1', 'Dr. Evaluator 2'],
                tasks: [
                    { task: 'Document Review', status: 'Pending', dueDate: visitDate },
                    { task: 'Team Assignment', status: 'Pending', dueDate: visitDate },
                    { task: 'Schedule Coordination', status: 'Pending', dueDate: visitDate },
                    { task: 'Final Preparation', status: 'Pending', dueDate: visitDate }
                ]
            };

            preVisitStageData.push(newPreVisit);
            loadPreVisitStage();
            showToast('Pre-visit plan created successfully!');
        }

        function openVisitManagementModal() { 
            // Create a new formal visit
            const programId = prompt('Enter Program ID (1-7):');
            if (!programId || isNaN(programId)) {
                showToast('Invalid Program ID', 'error');
                return;
            }

            const program = getEntityById(programs, parseInt(programId));
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const visitDate = prompt('Enter visit date (YYYY-MM-DD):', '2024-12-31');
            if (!visitDate) return;

            const visitType = prompt('Enter visit type:', 'Initial Accreditation Visit');
            if (!visitType) return;

            const newVisit = {
                id: generateId(formalVisitStageData),
                programId: parseInt(programId),
                visitType: visitType,
                visitDate: visitDate,
                visitDuration: '3 days',
                evaluator: 'PAASCU',
                teamLead: 'Dr. Team Lead',
                teamMembers: ['Dr. Member 1', 'Dr. Member 2', 'Dr. Member 3'],
                status: 'Scheduled',
                schedule: [
                    {
                        day: 'Day 1',
                        activities: ['Opening Ceremony', 'Document Review', 'Faculty Interview']
                    },
                    {
                        day: 'Day 2',
                        activities: ['Facility Tour', 'Student Interview', 'Administrative Review']
                    },
                    {
                        day: 'Day 3',
                        activities: ['Final Assessment', 'Exit Conference', 'Report Preparation']
                    }
                ]
            };

            formalVisitStageData.push(newVisit);
            loadFormalVisitStage();
            showToast('Formal visit scheduled successfully!');
        }

        function openPostAccreditationModal() { 
            // Create a new post-accreditation record
            const programId = prompt('Enter Program ID (1-7):');
            if (!programId || isNaN(programId)) {
                showToast('Invalid Program ID', 'error');
                return;
            }

            const program = getEntityById(programs, parseInt(programId));
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const level = prompt('Enter accreditation level (Level I/II/III/IV):', 'Level II');
            if (!level) return;

            const validUntil = prompt('Enter validity date (YYYY-MM-DD):', '2029-12-31');
            if (!validUntil) return;

            const newRecord = {
                id: generateId(postAccreditationData),
                programId: parseInt(programId),
                accreditationLevel: level,
                status: 'Accredited',
                validUntil: validUntil,
                recommendations: Math.floor(Math.random() * 5) + 1,
                complianceStatus: 'Compliant',
                nextAction: 'Annual Report Submission',
                nextActionDate: '2025-06-30'
            };

            postAccreditationData.push(newRecord);
            loadPostAccreditation();
            showToast('Post-accreditation record created successfully!');
        }

        // Action functions (placeholders)
        function viewApplication(id) { 
            const application = applicationStageData.find(app => app.id === id);
            if (!application) return;

            const program = getEntityById(programs, application.programId);
            const completedTasks = application.checklist.filter(item => item.completed).length;
            
            const details = `Application Details for ${program ? program.name : 'Unknown Program'}:
            
Status: ${application.status}
Progress: ${application.progress}%
Start Date: ${formatDate(application.startDate)}
Target Submission: ${formatDate(application.targetSubmission)}
Tasks Completed: ${completedTasks}/${application.checklist.length}

Checklist:
${application.checklist.map(item => `• ${item.item}: ${item.completed ? '✓ Completed' : '✗ Pending'}`).join('\n')}`;
            
            alert(details);
        }

        function editApplication(id) { 
            const application = applicationStageData.find(app => app.id === id);
            if (!application) return;

            const newProgress = Math.min(100, application.progress + 25);
            application.progress = newProgress;
            
            // Update checklist based on progress
            const itemsToComplete = Math.floor((newProgress / 100) * application.checklist.length);
            application.checklist.forEach((item, index) => {
                if (index < itemsToComplete) item.completed = true;
            });
            
            // Update status based on progress
            if (newProgress >= 100) {
                application.status = 'Ready for Submission';
            } else if (newProgress >= 75) {
                application.status = 'Documents Preparation';
            } else if (newProgress >= 25) {
                application.status = 'In Progress';
            }
            
            loadApplicationStage();
            showToast(`Application progress updated to ${newProgress}%!`);
        }

        function exportApplication(id) { 
            const application = applicationStageData.find(app => app.id === id);
            if (!application) return;
            
            const program = getEntityById(programs, application.programId);
            showToast(`Exporting application data for ${program ? program.name : 'Program'}... (simulated)`);
        }

        function viewSurveyProgress(id) { 
            const survey = selfSurveyStageData.find(s => s.id === id);
            if (!survey) return;

            const program = getEntityById(programs, survey.programId);
            const details = `Self-Survey Progress for ${program ? program.name : 'Unknown Program'}:
            
Overall Progress: ${survey.overallProgress}%
Status: ${survey.status}
Start Date: ${formatDate(survey.startDate)}
Deadline: ${formatDate(survey.deadline)}

Area Progress:
${survey.areas.map(area => `• ${area.area}: ${area.progress}% (${area.status})`).join('\n')}`;
            
            alert(details);
        }

        function editSurveyProgress(id) { 
            const survey = selfSurveyStageData.find(s => s.id === id);
            if (!survey) return;

            // Update a random area progress
            const randomAreaIndex = Math.floor(Math.random() * survey.areas.length);
            const area = survey.areas[randomAreaIndex];
            const progressIncrease = Math.min(100 - area.progress, 25);
            
            area.progress += progressIncrease;
            
            // Update area status based on progress
            if (area.progress >= 100) {
                area.status = 'Completed';
            } else if (area.progress > 0) {
                area.status = 'In Progress';
            }
            
            // Update overall progress
            survey.overallProgress = Math.round(survey.areas.reduce((sum, a) => sum + a.progress, 0) / survey.areas.length);
            
            // Update survey status
            if (survey.overallProgress >= 100) {
                survey.status = 'Completed';
            } else if (survey.overallProgress > 0) {
                survey.status = 'In Progress';
            }
            
            loadSelfSurveyStage();
            showToast(`Updated ${area.area} progress to ${area.progress}%!`);
        }

        function exportSurveyProgress(id) { 
            const survey = selfSurveyStageData.find(s => s.id === id);
            if (!survey) return;
            
            const program = getEntityById(programs, survey.programId);
            showToast(`Exporting survey progress for ${program ? program.name : 'Program'}... (simulated)`);
        }

        function viewPreVisitDetails(id) { 
            const preVisit = preVisitStageData.find(pv => pv.id === id);
            if (!preVisit) return;

            const program = getEntityById(programs, preVisit.programId);
            const completedTasks = preVisit.tasks.filter(task => task.status === 'Completed').length;
            
            const details = `Pre-Visit Details for ${program ? program.name : 'Unknown Program'}:
            
Status: ${preVisit.status}
Visit Date: ${formatDate(preVisit.visitDate)}
Tasks Completed: ${completedTasks}/${preVisit.tasks.length}

Evaluator Team:
${preVisit.evaluatorTeam.map(member => `• ${member}`).join('\n')}

Tasks:
${preVisit.tasks.map(task => `• ${task.task}: ${task.status} (Due: ${formatDate(task.dueDate)})`).join('\n')}`;
            
            alert(details);
        }

        function updatePreVisitStatus(id) { 
            const preVisit = preVisitStageData.find(pv => pv.id === id);
            if (!preVisit) return;

            // Find the next pending task and mark it as completed
            const nextTask = preVisit.tasks.find(task => task.status === 'Pending');
            if (nextTask) {
                nextTask.status = 'Completed';
                
                // Update overall status
                const completedTasks = preVisit.tasks.filter(task => task.status === 'Completed').length;
                const totalTasks = preVisit.tasks.length;
                
                if (completedTasks === totalTasks) {
                    preVisit.status = 'Ready for Visit';
                } else if (completedTasks > 0) {
                    preVisit.status = 'In Progress';
                }
                
                loadPreVisitStage();
                showToast(`Task "${nextTask.task}" marked as completed!`);
            } else {
                showToast('All tasks are already completed!', 'info');
            }
        }

        function rescheduleVisit(id) { 
            const preVisit = preVisitStageData.find(pv => pv.id === id);
            if (!preVisit) return;

            const newDate = prompt('Enter new visit date (YYYY-MM-DD):', preVisit.visitDate);
            if (newDate && newDate !== preVisit.visitDate) {
                preVisit.visitDate = newDate;
                
                // Update all task due dates
                preVisit.tasks.forEach(task => {
                    task.dueDate = newDate;
                });
                
                loadPreVisitStage();
                showToast(`Visit rescheduled to ${formatDate(newDate)}!`);
            }
        }

        function viewVisitDetails(id) { 
            const visit = formalVisitStageData.find(v => v.id === id);
            if (!visit) return;

            const program = getEntityById(programs, visit.programId);
            let details = `Formal Visit Details for ${program ? program.name : 'Unknown Program'}:
            
Visit Type: ${visit.visitType}
Date: ${formatDate(visit.visitDate)}
Duration: ${visit.visitDuration}
Evaluator: ${visit.evaluator}
Status: ${visit.status}
Team Lead: ${visit.teamLead}

Team Members:
${visit.teamMembers.map(member => `• ${member}`).join('\n')}`;

            if (visit.status === 'Completed') {
                details += `\n\nFinal Rating: ${visit.finalRating}
Recommendations: ${visit.recommendations}`;
            } else if (visit.schedule) {
                details += `\n\nSchedule:
${visit.schedule.map(day => `${day.day}:\n${day.activities.map(activity => `  • ${activity}`).join('\n')}`).join('\n\n')}`;
            }
            
            alert(details);
        }

        function manageVisit(id) { 
            const visit = formalVisitStageData.find(v => v.id === id);
            if (!visit) return;

            if (visit.status === 'Scheduled') {
                const action = confirm('Mark this visit as completed?');
                if (action) {
                    visit.status = 'Completed';
                    visit.finalRating = 'Level II';
                    visit.recommendations = Math.floor(Math.random() * 5) + 1;
                    
                    loadFormalVisitStage();
                    showToast('Visit marked as completed!');
                }
            } else if (visit.status === 'Completed') {
                showToast('Visit is already completed!', 'info');
            } else {
                showToast('Visit management options not available for this status.', 'warning');
            }
        }

        function viewVisitReport(id) { 
            const visit = formalVisitStageData.find(v => v.id === id);
            if (!visit) return;

            if (visit.status === 'Completed') {
                const program = getEntityById(programs, visit.programId);
                showToast(`Viewing visit report for ${program ? program.name : 'Program'}... (simulated)`);
            } else {
                showToast('Visit report not available until visit is completed.', 'warning');
            }
        }

        function viewAccreditationDetails(id) { 
            const record = postAccreditationData.find(r => r.id === id);
            if (!record) return;

            const program = getEntityById(programs, record.programId);
            const daysUntilExpiry = Math.ceil((new Date(record.validUntil) - new Date()) / (1000 * 60 * 60 * 24));
            
            const details = `Accreditation Details for ${program ? program.name : 'Unknown Program'}:
            
Level: ${record.accreditationLevel}
Status: ${record.status}
Valid Until: ${formatDate(record.validUntil)} (${daysUntilExpiry} days remaining)
Recommendations: ${record.recommendations}
Compliance Status: ${record.complianceStatus}
Next Action: ${record.nextAction}
Next Action Date: ${formatDate(record.nextActionDate)}
Certificate: ${record.certificate}`;
            
            alert(details);
        }

        function downloadCertificate(id) { 
            const record = postAccreditationData.find(r => r.id === id);
            if (!record) return;

            const program = getEntityById(programs, record.programId);
            showToast(`Downloading certificate ${record.certificate} for ${program ? program.name : 'Program'}... (simulated)`);
        }

        function updateCompliance(id) { 
            const record = postAccreditationData.find(r => r.id === id);
            if (!record) return;

            const statuses = ['Compliant', 'Monitoring', 'Action Required', 'Non-Compliant'];
            const currentIndex = statuses.indexOf(record.complianceStatus);
            const nextIndex = (currentIndex + 1) % statuses.length;
            
            record.complianceStatus = statuses[nextIndex];
            
            // Update next action based on compliance status
            switch (record.complianceStatus) {
                case 'Compliant':
                    record.nextAction = 'Annual Report Due';
                    break;
                case 'Monitoring':
                    record.nextAction = 'Quarterly Review';
                    break;
                case 'Action Required':
                    record.nextAction = 'Submit Compliance Report';
                    break;
                case 'Non-Compliant':
                    record.nextAction = 'Immediate Action Required';
                    break;
            }
            
            loadPostAccreditation();
            showToast(`Compliance status updated to: ${record.complianceStatus}`);
        }

        function viewRecommendations(id) { 
            const record = postAccreditationData.find(r => r.id === id);
            if (!record) return;

            const program = getEntityById(programs, record.programId);
            const sampleRecommendations = [
                "Enhance faculty development programs",
                "Improve laboratory facilities",
                "Strengthen industry partnerships",
                "Update curriculum standards",
                "Expand library resources"
            ];

            const recommendations = sampleRecommendations.slice(0, record.recommendations);
            const details = `Recommendations for ${program ? program.name : 'Unknown Program'}:

${recommendations.map((rec, index) => `${index + 1}. ${rec}`).join('\n')}`;
            
            alert(details);
        }

        // Filter functions
        function filterApplications() { loadApplicationStage(); }
        function filterSurveyStage() { loadSelfSurveyStage(); }
        function filterPreVisit() { loadPreVisitStage(); }
        function filterFormalVisits() { loadFormalVisitStage(); }
        function filterPostAccreditation() { loadPostAccreditation(); }

        // Cycle Management Functions
        function saveCycle() {
            if (!validateForm('addCycleForm')) return;

            const newCycle = {
                id: generateId(cycles),
                type: document.getElementById('cycleType').value,
                duration: document.getElementById('cycleDuration').value,
                status: document.getElementById('cycleStatus').value,
                priority: document.getElementById('cyclePriority').value,
                description: document.getElementById('cycleDescription').value
            };

            cycles.push(newCycle);
            closeModal('addCycleModal');
            resetForm('addCycleForm');
            showToast('Cycle added successfully!');
        }

        function updateCycle() {
            if (!validateForm('editCycleForm')) return;

            const cycleIndex = cycles.findIndex(c => c.id === currentEditingCycleId);
            if (cycleIndex === -1) return;

            cycles[cycleIndex] = {
                ...cycles[cycleIndex],
                type: document.getElementById('editCycleType').value,
                duration: document.getElementById('editCycleDuration').value,
                status: document.getElementById('editCycleStatus').value,
                priority: document.getElementById('editCyclePriority').value,
                description: document.getElementById('editCycleDescription').value
            };

            closeModal('editCycleModal');
            showToast('Cycle updated successfully!');
        }
    </script>

    <style>
        .area-card {
            transition: all 0.3s ease;
        }
        
        .area-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .timeline-item {
            border-left: 2px solid #e9ecef;
            padding-left: 1rem;
            margin-left: 0.5rem;
        }
        
        .timeline-marker {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: -16px;
        }
        
        .visit-schedule {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
        }
        
        .checklist .form-check {
            margin-bottom: 0.5rem;
        }
        
        .checklist .form-check-label.text-success {
            text-decoration: line-through;
        }
    </style>
</body>
</html> 