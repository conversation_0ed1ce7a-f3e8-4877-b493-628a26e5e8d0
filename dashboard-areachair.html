<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Area Chair Dashboard - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard-areachair.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Area Chair</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col">
                    <h1 class="h3 text-nd-green mb-0">Area Chair Dashboard</h1>
                    <p class="text-muted">Manage your assigned programs and departments</p>
                </div>
            </div>

            <!-- Alert for Data Sync -->
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-sync-alt me-2"></i>
                <strong>Connected to Central System:</strong> All changes you make are automatically synced with the main administration system and visible to superadmin/admin users.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stat-card bg-primary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">My Programs</h5>
                                    <h2 class="mb-0" id="myProgramsCount">0</h2>
                                    <small>Assigned to me</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-graduation-cap fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stat-card bg-warning text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Pending Reviews</h5>
                                    <h2 class="mb-0" id="pendingReviewsCount">0</h2>
                                    <small>Documents to review</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clipboard-check fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stat-card bg-success text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Active Schedules</h5>
                                    <h2 class="mb-0" id="activeSchedulesCount">0</h2>
                                    <small>In my areas</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stat-card bg-info text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">My Activities</h5>
                                    <h2 class="mb-0" id="myActivitiesCount">0</h2>
                                    <small>This month</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions & My Activities -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-nd-green text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-6">
                                    <button class="btn btn-outline-nd-green w-100 h-100 d-flex flex-column justify-content-center" onclick="createNewSchedule()">
                                        <i class="fas fa-plus fa-2x mb-2"></i>
                                        <span>Add Schedule</span>
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-outline-nd-green w-100 h-100 d-flex flex-column justify-content-center" onclick="reviewDocuments()">
                                        <i class="fas fa-eye fa-2x mb-2"></i>
                                        <span>Review Documents</span>
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-outline-nd-green w-100 h-100 d-flex flex-column justify-content-center" onclick="approveDocument()">
                                        <i class="fas fa-check fa-2x mb-2"></i>
                                        <span>Approve Documents</span>
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-outline-nd-green w-100 h-100 d-flex flex-column justify-content-center" onclick="viewReports()">
                                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                        <span>View Reports</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-nd-green text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>My Recent Activities</h5>
                        </div>
                        <div class="card-body">
                            <div class="activity-feed" id="myActivitiesFeed">
                                <!-- Activities will be populated dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Programs and Schedules -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-nd-green text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-tasks me-2"></i>My Assigned Programs & Schedules</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Department</th>
                                            <th>Next Visit</th>
                                            <th>Status</th>
                                            <th>Priority</th>
                                            <th>Last Modified</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="myProgramsTableBody">
                                        <!-- Programs will be populated dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Create Schedule Modal -->
    <div class="modal fade" id="createScheduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Create New Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createScheduleForm">
                        <div class="mb-3">
                            <label class="form-label">Program <span class="text-danger">*</span></label>
                            <select class="form-select" id="newScheduleProgram" required>
                                <option value="">Select Program</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Agency <span class="text-danger">*</span></label>
                                    <select class="form-select" id="newScheduleAgency" required>
                                        <option value="">Select Agency</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Level <span class="text-danger">*</span></label>
                                    <select class="form-select" id="newScheduleLevel" required>
                                        <option value="">Select Level</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Cycle <span class="text-danger">*</span></label>
                                    <select class="form-select" id="newScheduleCycle" required>
                                        <option value="">Select Cycle</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Visit Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="newScheduleVisitDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <select class="form-select" id="newSchedulePriority">
                                        <option value="Normal">Normal</option>
                                        <option value="High">High</option>
                                        <option value="Urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" id="newScheduleNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveNewSchedule()">
                        <i class="fas fa-save me-2"></i>Create Schedule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Area Chair Dashboard JavaScript
        let currentUser = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            loadAreaChairDashboard();
        });

        // Load Area Chair specific dashboard data
        function loadAreaChairDashboard() {
            currentUser = getCurrentUser();
            if (!currentUser || currentUser.role !== 'areachair') {
                window.location.href = 'login.html';
                return;
            }

            loadMyStatistics();
            loadMyActivities();
            loadMyPrograms();
            populateNewScheduleDropdowns();
        }

        // Load statistics for area chair
        function loadMyStatistics() {
            const myPrograms = getUserAccessibleData(currentUser, 'programs');
            const mySchedules = getUserAccessibleData(currentUser, 'schedules');
            const myDocuments = getUserAccessibleData(currentUser, 'documents');
            const myActivities = getUserAccessibleData(currentUser, 'activityLog');

            // Update statistics cards
            document.getElementById('myProgramsCount').textContent = myPrograms.length;
            
            const pendingDocs = myDocuments.filter(d => d.status === 'Pending Review' || d.status === 'In Review');
            document.getElementById('pendingReviewsCount').textContent = pendingDocs.length;
            
            const activeSchedules = mySchedules.filter(s => s.status === 'In Progress' || s.status === 'Preparation');
            document.getElementById('activeSchedulesCount').textContent = activeSchedules.length;
            
            const thisMonth = new Date();
            const monthStart = new Date(thisMonth.getFullYear(), thisMonth.getMonth(), 1);
            const monthActivities = myActivities.filter(a => new Date(a.timestamp) >= monthStart);
            document.getElementById('myActivitiesCount').textContent = monthActivities.length;
        }

        // Load my recent activities
        function loadMyActivities() {
            const activitiesFeed = document.getElementById('myActivitiesFeed');
            const myActivities = getUserAccessibleData(currentUser, 'activityLog').slice(0, 5);
            
            activitiesFeed.innerHTML = '';
            
            if (myActivities.length === 0) {
                activitiesFeed.innerHTML = `
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>No recent activities</p>
                    </div>
                `;
                return;
            }
            
            myActivities.forEach(activity => {
                const timeAgo = getTimeAgo(activity.timestamp);
                const iconClass = getActivityIcon(activity.action);
                const bgClass = getActivityBgClass(activity.action);
                
                const activityHtml = `
                    <div class="activity-item">
                        <div class="activity-icon ${bgClass}">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="activity-content">
                            <strong>${activity.description}</strong>
                            <small class="text-muted d-block">${timeAgo}</small>
                        </div>
                    </div>
                `;
                activitiesFeed.innerHTML += activityHtml;
            });
        }

        // Load my programs table
        function loadMyPrograms() {
            const tableBody = document.getElementById('myProgramsTableBody');
            const myPrograms = getUserAccessibleData(currentUser, 'programs');
            const mySchedules = getUserAccessibleData(currentUser, 'schedules');
            
            tableBody.innerHTML = '';
            
            myPrograms.forEach(program => {
                const department = getEntityById(departments, program.departmentId);
                const programSchedules = mySchedules.filter(s => s.programId === program.id);
                const upcomingSchedule = programSchedules
                    .filter(s => new Date(s.visitDate) >= new Date())
                    .sort((a, b) => new Date(a.visitDate) - new Date(b.visitDate))[0];
                
                const latestSchedule = programSchedules
                    .sort((a, b) => new Date(b.lastModifiedDate) - new Date(a.lastModifiedDate))[0];
                
                const row = `
                    <tr>
                        <td><strong>${program.name}</strong></td>
                        <td>${department ? department.name : 'N/A'}</td>
                        <td>${upcomingSchedule ? formatDate(upcomingSchedule.visitDate) : 'No upcoming visit'}</td>
                        <td>${upcomingSchedule ? getStatusBadge(upcomingSchedule.status) : '<span class="badge bg-secondary">No Schedule</span>'}</td>
                        <td>${upcomingSchedule ? getPriorityBadge(upcomingSchedule.priority) : '-'}</td>
                        <td>${latestSchedule ? getTimeAgo(latestSchedule.lastModifiedDate) : 'Never'}</td>
                        <td>
                            ${upcomingSchedule ? 
                                `<button class="btn btn-sm btn-outline-primary" onclick="editSchedule(${upcomingSchedule.id})">Edit</button>` :
                                `<button class="btn btn-sm btn-outline-success" onclick="createScheduleForProgram(${program.id})">Create Schedule</button>`
                            }
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        // Quick action functions
        function createNewSchedule() {
            openModal('createScheduleModal');
        }

        function reviewDocuments() {
            window.location.href = 'documents.html?filter=pending';
        }

        function approveDocument() {
            window.location.href = 'documents.html?filter=review';
        }

        function viewReports() {
            window.location.href = 'reports.html';
        }

        // Create schedule for specific program
        function createScheduleForProgram(programId) {
            document.getElementById('newScheduleProgram').value = programId;
            openModal('createScheduleModal');
        }

        // Populate dropdowns for new schedule
        function populateNewScheduleDropdowns() {
            const myPrograms = getUserAccessibleData(currentUser, 'programs');
            
            // Populate programs
            const programSelect = document.getElementById('newScheduleProgram');
            programSelect.innerHTML = '<option value="">Select Program</option>';
            myPrograms.forEach(program => {
                programSelect.innerHTML += `<option value="${program.id}">${program.name}</option>`;
            });

            // Populate agencies, levels, cycles
            populateSelect('newScheduleAgency', agencies, 'name');
            populateSelect('newScheduleLevel', levels, 'level');
            populateSelect('newScheduleCycle', cycles, 'type');
        }

        // Save new schedule
        function saveNewSchedule() {
            if (!validateForm('createScheduleForm')) return;

            const program = getEntityById(programs, parseInt(document.getElementById('newScheduleProgram').value));
            
            const scheduleData = {
                programId: parseInt(document.getElementById('newScheduleProgram').value),
                departmentId: program ? program.departmentId : null,
                agencyId: parseInt(document.getElementById('newScheduleAgency').value),
                levelId: parseInt(document.getElementById('newScheduleLevel').value),
                cycleId: parseInt(document.getElementById('newScheduleCycle').value),
                coveragePeriod: `AY ${new Date().getFullYear()}-${new Date().getFullYear() + 3}`,
                visitDate: document.getElementById('newScheduleVisitDate').value,
                status: 'Preparation',
                priority: document.getElementById('newSchedulePriority').value || 'Normal',
                notes: document.getElementById('newScheduleNotes').value || ''
            };

            const result = createScheduleWithTracking(scheduleData);
            if (result) {
                loadAreaChairDashboard(); // Refresh dashboard
                closeModal('createScheduleModal');
                showToast('Schedule created successfully! This change is now visible to all administrators.', 'success');
            }
        }

        // Edit existing schedule (redirect to schedules page)
        function editSchedule(scheduleId) {
            window.location.href = `schedules.html?edit=${scheduleId}`;
        }

        // Helper functions
        function getStatusBadge(status) {
            const statusMap = {
                'Preparation': 'bg-info',
                'In Progress': 'bg-warning',
                'Completed': 'bg-success',
                'Postponed': 'bg-danger'
            };
            return `<span class="badge ${statusMap[status] || 'bg-secondary'}">${status}</span>`;
        }

        function getPriorityBadge(priority) {
            const priorityMap = {
                'Urgent': 'bg-danger',
                'High': 'bg-warning',
                'Normal': 'bg-success'
            };
            return `<span class="badge ${priorityMap[priority] || 'bg-secondary'}">${priority}</span>`;
        }

        function getActivityIcon(action) {
            switch(action) {
                case 'Schedule Created': return 'fas fa-plus';
                case 'Schedule Updated': return 'fas fa-edit';
                case 'Document Uploaded': return 'fas fa-upload';
                case 'Document Approved': return 'fas fa-check';
                case 'Document Review Started': return 'fas fa-eye';
                default: return 'fas fa-info';
            }
        }

        function getActivityBgClass(action) {
            switch(action) {
                case 'Schedule Created': return 'bg-primary';
                case 'Schedule Updated': return 'bg-warning';
                case 'Document Uploaded': return 'bg-info';
                case 'Document Approved': return 'bg-success';
                case 'Document Review Started': return 'bg-warning';
                default: return 'bg-secondary';
            }
        }

        function getTimeAgo(timestamp) {
            const now = new Date();
            const activityTime = new Date(timestamp);
            const diffMs = now - activityTime;
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            
            if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
            if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
            if (diffMinutes > 0) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
            return 'Just now';
        }

        function populateSelect(selectId, data, textField) {
            const select = document.getElementById(selectId);
            select.innerHTML = '<option value="">Select...</option>';
            data.forEach(item => {
                select.innerHTML += `<option value="${item.id}">${item[textField]}</option>`;
            });
        }

        function openModal(modalId) {
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();
        }

        function closeModal(modalId) {
            const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
            if (modal) {
                modal.hide();
            }
        }
    </script>

    <style>
        .activity-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #dee2e6;
        }

        .activity-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        .activity-content {
            flex-grow: 1;
        }

        .stat-card {
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }
    </style>
</body>
</html> 