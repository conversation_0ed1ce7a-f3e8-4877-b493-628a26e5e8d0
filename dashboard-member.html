<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard-member.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="viewMyDocuments()"><i class="fas fa-folder me-2"></i>My Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Member</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-cog me-1"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Welcome Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-user me-3"></i>Member Dashboard</h1>
                    <p>Upload and manage your documents for accreditation review</p>
                </div>
            </div>

            <!-- Access Notice -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Member Access:</strong> You can upload and view your own documents only. For document review and approval, please contact your Area Chair or Sub-Area Chair.
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-primary">
                        <div class="stat-icon">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="myDocuments">0</h3>
                            <p>My Documents</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-success">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="approvedDocs">0</h3>
                            <p>Approved</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pendingDocs">0</h3>
                            <p>Pending Review</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-danger">
                        <div class="stat-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="needsRevision">0</h3>
                            <p>Needs Revision</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="row">
                <!-- Document Upload & My Documents -->
                <div class="col-lg-8">
                    <!-- Quick Upload -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-upload me-2"></i>Quick Document Upload</h5>
                        </div>
                        <div class="card-body">
                            <form id="quickUploadForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <input type="file" class="form-control" id="documentFile" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx">
                                        <div class="form-text">Supported: PDF, Word, Excel, PowerPoint</div>
                                    </div>
                                    <div class="col-md-4">
                                        <select class="form-select" id="documentArea">
                                            <option value="">Select Area</option>
                                            <option value="1">Area 1: Leadership and Governance</option>
                                            <option value="2">Area 2: Quality Assurance</option>
                                            <option value="3">Area 3: Resource Management</option>
                                            <option value="4">Area 4: Teaching-Learning</option>
                                            <option value="5">Area 5: Student Services</option>
                                            <option value="6">Area 6: External Relations</option>
                                            <option value="7">Area 7: Research</option>
                                            <option value="8">Area 8: Results</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-upload me-1"></i>Upload
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- My Documents -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0"><i class="fas fa-folder-open me-2"></i>My Documents</h5>
                            <button class="btn btn-primary btn-sm" onclick="openUploadModal()">
                                <i class="fas fa-plus me-1"></i>Add Document
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Document Name</th>
                                            <th>Area</th>
                                            <th>Upload Date</th>
                                            <th>Status</th>
                                            <th>Feedback</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="myDocumentsTable">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">Loading your documents...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions & Status -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="openUploadModal()">
                                    <i class="fas fa-upload me-2"></i>Upload New Document
                                </button>
                                <button class="btn btn-success" onclick="viewMyDocuments()">
                                    <i class="fas fa-folder-open me-2"></i>View All My Documents
                                </button>
                                <button class="btn btn-info" onclick="checkDocumentStatus()">
                                    <i class="fas fa-search me-2"></i>Check Document Status
                                </button>
                                <button class="btn btn-warning" onclick="viewFeedback()">
                                    <i class="fas fa-comments me-2"></i>View Feedback
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentActivity">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Upload Guidelines -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-lightbulb me-2"></i>Upload Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use descriptive file names</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Select appropriate area</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Files under 50MB</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>PDF format preferred</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Include version numbers</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Upload Modal -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-upload me-2"></i>Upload Document</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="detailedUploadForm">
                        <div class="mb-3">
                            <label class="form-label">Select File</label>
                            <input type="file" class="form-control" id="modalDocumentFile" accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx">
                            <div class="form-text">Supported formats: PDF, Word, Excel, PowerPoint (Max: 50MB)</div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Accreditation Area <span class="text-danger">*</span></label>
                                    <select class="form-select" id="modalDocumentArea" required>
                                        <option value="">Select Area</option>
                                        <option value="1">Area 1: Leadership and Governance</option>
                                        <option value="2">Area 2: Quality Assurance</option>
                                        <option value="3">Area 3: Resource Management</option>
                                        <option value="4">Area 4: Teaching-Learning</option>
                                        <option value="5">Area 5: Student Services</option>
                                        <option value="6">Area 6: External Relations</option>
                                        <option value="7">Area 7: Research</option>
                                        <option value="8">Area 8: Results</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Document Type</label>
                                    <select class="form-select" id="modalDocumentType">
                                        <option value="">Select Type</option>
                                        <option value="policy">Policy Document</option>
                                        <option value="report">Report</option>
                                        <option value="evidence">Supporting Evidence</option>
                                        <option value="form">Form/Template</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Document Title</label>
                            <input type="text" class="form-control" id="modalDocumentTitle" placeholder="Enter a descriptive title">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="modalDocumentDescription" rows="3" placeholder="Brief description of the document and its purpose"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Keywords (comma-separated)</label>
                            <input type="text" class="form-control" id="modalDocumentKeywords" placeholder="e.g., policy, 2024, strategic plan">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="uploadDocument()">
                        <i class="fas fa-upload me-1"></i>Upload Document
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        let currentUser = null;
        
        // Quality Areas mapping
        const areaNames = {
            1: 'Leadership and Governance',
            2: 'Quality Assurance',
            3: 'Resource Management',
            4: 'Teaching-Learning',
            5: 'Student Services',
            6: 'External Relations',
            7: 'Research',
            8: 'Results'
        };

        // Authentication check
        function checkAuthentication() {
            const user = sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return false;
            }
            
            const userData = JSON.parse(user);
            if (userData.role !== 'member') {
                alert('Access denied. This dashboard is only for Members.');
                window.location.href = 'login.html';
                return false;
            }
            
            return userData;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            currentUser = checkAuthentication();
            if (currentUser) {
                initializeDashboard();
            }
        });

        function initializeDashboard() {
            // Update user display
            document.getElementById('userDisplayName').textContent = currentUser.name;
            
            // Load statistics
            loadStatistics();
            
            // Load my documents
            loadMyDocuments();
            
            // Load recent activity
            loadRecentActivity();
            
            // Set up form handlers
            setupFormHandlers();
        }

        function loadStatistics() {
            // Simulate member's document statistics
            document.getElementById('myDocuments').textContent = '12';
            document.getElementById('approvedDocs').textContent = '8';
            document.getElementById('pendingDocs').textContent = '3';
            document.getElementById('needsRevision').textContent = '1';
        }

        function loadMyDocuments() {
            const tbody = document.getElementById('myDocumentsTable');
            
            // Sample user documents with different statuses
            const myDocs = [
                { id: 1, name: 'Teaching Portfolio 2024.pdf', area: 4, date: '2024-12-15', status: 'Approved', feedback: 'Good' },
                { id: 2, name: 'Research Publication List.docx', area: 7, date: '2024-12-14', status: 'Pending Review', feedback: 'None' },
                { id: 3, name: 'Student Evaluation Summary.xlsx', area: 4, date: '2024-12-13', status: 'Needs Revision', feedback: 'Available' },
                { id: 4, name: 'Community Service Report.pdf', area: 6, date: '2024-12-12', status: 'Approved', feedback: 'Excellent' },
                { id: 5, name: 'Professional Development Plan.docx', area: 3, date: '2024-12-11', status: 'Pending Review', feedback: 'None' }
            ];
            
            tbody.innerHTML = myDocs.map(doc => `
                <tr>
                    <td><strong>${doc.name}</strong></td>
                    <td><span class="badge bg-info">Area ${doc.area}</span><br><small class="text-muted">${areaNames[doc.area]}</small></td>
                    <td>${formatDate(doc.date)}</td>
                    <td><span class="badge ${getStatusBadgeClass(doc.status)}">${doc.status}</span></td>
                    <td>
                        ${doc.feedback !== 'None' ? 
                            `<button class="btn btn-sm btn-outline-info" onclick="viewFeedbackDetail(${doc.id})">View</button>` : 
                            '<span class="text-muted">None</span>'
                        }
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="viewDocument(${doc.id})" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${doc.status === 'Needs Revision' ? 
                            `<button class="btn btn-sm btn-outline-warning" onclick="reviseDocument(${doc.id})" title="Revise">
                                <i class="fas fa-edit"></i>
                            </button>` : ''
                        }
                    </td>
                </tr>
            `).join('');
        }

        function loadRecentActivity() {
            const container = document.getElementById('recentActivity');
            const activities = [
                { action: 'Document approved', item: 'Teaching Portfolio 2024.pdf', time: '2 hours ago', icon: 'check-circle', color: 'success' },
                { action: 'Document uploaded', item: 'Research Publication List.docx', time: '1 day ago', icon: 'upload', color: 'primary' },
                { action: 'Feedback received', item: 'Student Evaluation Summary.xlsx', time: '2 days ago', icon: 'comment', color: 'warning' },
                { action: 'Document approved', item: 'Community Service Report.pdf', time: '3 days ago', icon: 'check-circle', color: 'success' }
            ];
            
            container.innerHTML = activities.map(activity => `
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-${activity.icon} text-${activity.color}"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold">${activity.action}</div>
                        <small class="text-muted">${activity.item}</small>
                        <br><small class="text-muted">${activity.time}</small>
                    </div>
                </div>
            `).join('');
        }

        function setupFormHandlers() {
            // Quick upload form
            document.getElementById('quickUploadForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const files = document.getElementById('documentFile').files;
                const area = document.getElementById('documentArea').value;
                
                if (files.length === 0) {
                    showToast('Please select a file to upload', 'warning');
                    return;
                }
                
                if (!area) {
                    showToast('Please select an accreditation area', 'warning');
                    return;
                }
                
                simulateQuickUpload(files[0].name, area);
            });
        }

        function getStatusBadgeClass(status) {
            switch(status) {
                case 'Approved': return 'bg-success';
                case 'Pending Review': return 'bg-warning';
                case 'Needs Revision': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        // Action functions
        function openUploadModal() {
            const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
            modal.show();
        }

        function uploadDocument() {
            const file = document.getElementById('modalDocumentFile').files[0];
            const area = document.getElementById('modalDocumentArea').value;
            const title = document.getElementById('modalDocumentTitle').value;
            
            if (!file) {
                showToast('Please select a file to upload', 'warning');
                return;
            }
            
            if (!area) {
                showToast('Please select an accreditation area', 'warning');
                return;
            }
            
            simulateDetailedUpload(file.name, area, title);
            const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
            modal.hide();
        }

        function simulateQuickUpload(fileName, area) {
            showToast(`Uploading ${fileName} to Area ${area}...`, 'info');
            
            setTimeout(() => {
                showToast(`${fileName} uploaded successfully!`, 'success');
                loadMyDocuments();
                loadStatistics();
                loadRecentActivity();
            }, 2000);
        }

        function simulateDetailedUpload(fileName, area, title) {
            const displayTitle = title || fileName;
            showToast(`Uploading "${displayTitle}" to Area ${area}...`, 'info');
            
            setTimeout(() => {
                showToast(`"${displayTitle}" uploaded successfully!`, 'success');
                loadMyDocuments();
                loadStatistics();
                loadRecentActivity();
                
                // Reset form
                document.getElementById('detailedUploadForm').reset();
            }, 2000);
        }

        function viewMyDocuments() {
            showToast('Loading your complete document list...', 'info');
        }

        function checkDocumentStatus() {
            showToast('Checking status of all your documents...', 'info');
        }

        function viewFeedback() {
            alert('Feedback Summary:\n\n• Teaching Portfolio 2024.pdf: Excellent work, well organized\n• Student Evaluation Summary.xlsx: Please include more detailed analysis\n• Community Service Report.pdf: Outstanding contribution documentation');
        }

        function viewFeedbackDetail(docId) {
            // Sample feedback based on document ID
            const feedbacks = {
                1: 'Excellent work! Your teaching portfolio is well-organized and demonstrates clear professional growth.',
                3: 'Please include more detailed analysis of student feedback trends and action plans for improvement.',
                4: 'Outstanding documentation of community service contributions. The impact is clearly demonstrated.'
            };
            
            const feedback = feedbacks[docId] || 'No specific feedback available.';
            alert(`Document Feedback:\n\n${feedback}`);
        }

        function viewDocument(docId) {
            showToast('Opening document viewer...', 'info');
        }

        function reviseDocument(docId) {
            if (confirm('Would you like to upload a revised version of this document?')) {
                openUploadModal();
            }
        }

        // Logout functionality
        function logoutUser() {
            if (confirm('Are you sure you want to logout?')) {
                sessionStorage.removeItem('currentUser');
                showToast('Logged out successfully!', 'success');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
            }
        }
    </script>
</body>
</html> 