<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sub-Area Chair Dashboard - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard-subareachair.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user-edit me-2"></i><span id="userDisplayName">Sub-Area Chair</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-cog me-1"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Welcome Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-user-edit me-3"></i>Sub-Area Chair Dashboard</h1>
                    <p>Upload, organize, and tag documents within your assigned sub-area</p>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-primary">
                        <div class="stat-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="assignedArea">0</h3>
                            <p>Assigned Area</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-success">
                        <div class="stat-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="uploadedDocuments">0</h3>
                            <p>Documents Uploaded</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-warning">
                        <div class="stat-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="taggedDocuments">0</h3>
                            <p>Documents Tagged</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-info">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="organizationRate">0%</h3>
                            <p>Organization Rate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assigned Area Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-star me-2"></i>Your Assigned Area</h5>
                        </div>
                        <div class="card-body">
                            <div id="assignedAreaInfo">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="row">
                <!-- Document Upload & Management -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0"><i class="fas fa-folder-open me-2"></i>Document Management</h5>
                            <button class="btn btn-primary btn-sm" onclick="openUploadModal()">
                                <i class="fas fa-plus me-1"></i>Upload Document
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- Document Upload Form -->
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                As a Sub-Area Chair, you can upload, organize, and tag documents within <strong id="areaNameDisplay">your assigned area</strong>.
                            </div>
                            
                            <!-- Quick Upload Form -->
                            <form id="quickUploadForm" class="mb-4">
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="file" class="form-control" id="documentFile" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx">
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-upload me-1"></i>Quick Upload
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <!-- Recent Documents -->
                            <h6 class="mb-3">Recent Documents</h6>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Document Name</th>
                                            <th>Type</th>
                                            <th>Upload Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recentDocumentsTable">
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">Loading documents...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions & Tools -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="openUploadModal()">
                                    <i class="fas fa-upload me-2"></i>Upload Documents
                                </button>
                                <button class="btn btn-success" onclick="organizeDocuments()">
                                    <i class="fas fa-sort-alpha-up me-2"></i>Organize Documents
                                </button>
                                <button class="btn btn-info" onclick="tagDocuments()">
                                    <i class="fas fa-tags me-2"></i>Tag Documents
                                </button>
                                <button class="btn btn-warning" onclick="viewAllDocuments()">
                                    <i class="fas fa-folder-open me-2"></i>View All Documents
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Document Categories -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-folder me-2"></i>Document Categories</h5>
                        </div>
                        <div class="card-body">
                            <div id="documentCategories">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Upload Progress -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i>Area Progress</h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center">
                                <div class="progress mb-3" style="height: 20px;">
                                    <div class="progress-bar bg-success" role="progressbar" id="areaProgress" style="width: 0%">0%</div>
                                </div>
                                <small class="text-muted">Documents completion rate for your area</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Upload Modal -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-upload me-2"></i>Upload Documents</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadForm">
                        <div class="mb-3">
                            <label class="form-label">Select Files</label>
                            <input type="file" class="form-control" id="modalDocumentFiles" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx">
                            <div class="form-text">Supported formats: PDF, Word, Excel, PowerPoint</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Document Category</label>
                            <select class="form-select" id="documentCategory">
                                <option value="">Select Category</option>
                                <option value="policies">Policies & Procedures</option>
                                <option value="reports">Reports & Assessments</option>
                                <option value="evidence">Supporting Evidence</option>
                                <option value="forms">Forms & Templates</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tags (comma-separated)</label>
                            <input type="text" class="form-control" id="documentTags" placeholder="e.g., policy, 2024, strategic">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="documentDescription" rows="3" placeholder="Brief description of the document(s)"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="uploadDocuments()">
                        <i class="fas fa-upload me-1"></i>Upload Documents
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        let currentUser = null;
        let assignedAreaData = null;
        
        // Quality Areas data
        const qualityAreas = [
            { id: 1, name: 'Leadership and Governance', standards: 4, description: 'Vision-Mission, Leadership Management, Strategic Management, Policy Formulation' },
            { id: 2, name: 'Quality Assurance', standards: 2, description: 'Internal Quality Assurance System, External Quality Assurance' },
            { id: 3, name: 'Resource Management', standards: 4, description: 'Human Resources, Financial Resources, Physical Facilities' },
            { id: 4, name: 'Teaching-Learning', standards: 3, description: 'Curricular Programs, Teaching and Learning Methods, Assessment Methods' },
            { id: 5, name: 'Student Services', standards: 2, description: 'Student Recruitment, Admission, and Placement, Student Services Programs and Support' },
            { id: 6, name: 'External Relations', standards: 2, description: 'Networks, Linkages, and Partnerships, Community Engagement and Service' },
            { id: 7, name: 'Research', standards: 2, description: 'Research Management and Collaboration, Intellectual Property Rights and Ethics in Research' },
            { id: 8, name: 'Results', standards: 4, description: 'Educational Results, Community Engagement and Service Results, Research Results, Financial and Competitiveness Results' }
        ];

        // Authentication check
        function checkAuthentication() {
            const user = sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return false;
            }
            
            const userData = JSON.parse(user);
            if (userData.role !== 'subareachair') {
                alert('Access denied. This dashboard is only for Sub-Area Chairs.');
                window.location.href = 'login.html';
                return false;
            }
            
            return userData;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            currentUser = checkAuthentication();
            if (currentUser) {
                initializeDashboard();
            }
        });

        function initializeDashboard() {
            // Update user display
            document.getElementById('userDisplayName').textContent = currentUser.name;
            
            // Load assigned area information
            loadAssignedArea();
            
            // Load statistics
            loadStatistics();
            
            // Load recent documents
            loadRecentDocuments();
            
            // Load document categories
            loadDocumentCategories();
            
            // Set up form handlers
            setupFormHandlers();
        }

        function loadAssignedArea() {
            if (!Array.isArray(currentUser.areas) || currentUser.areas.length === 0) {
                document.getElementById('assignedAreaInfo').innerHTML = '<p class="text-muted">No area assigned</p>';
                return;
            }
            
            const areaId = currentUser.areas[0]; // Sub-area chairs typically have one area
            assignedAreaData = qualityAreas.find(a => a.id === areaId);
            
            if (assignedAreaData) {
                document.getElementById('assignedArea').textContent = `Area ${assignedAreaData.id}`;
                document.getElementById('areaNameDisplay').textContent = `Area ${assignedAreaData.id}: ${assignedAreaData.name}`;
                
                const areaInfo = `
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-primary">Area ${assignedAreaData.id}: ${assignedAreaData.name}</h6>
                            <p class="text-muted">${assignedAreaData.description}</p>
                            <p><strong>Standards:</strong> ${assignedAreaData.standards} total standards</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 75%">75%</div>
                            </div>
                            <small class="text-muted">Area Completion</small>
                        </div>
                    </div>
                `;
                document.getElementById('assignedAreaInfo').innerHTML = areaInfo;
                document.getElementById('areaProgress').style.width = '75%';
                document.getElementById('areaProgress').textContent = '75%';
            }
        }

        function loadStatistics() {
            // Simulate statistics
            document.getElementById('uploadedDocuments').textContent = '24';
            document.getElementById('taggedDocuments').textContent = '18';
            document.getElementById('organizationRate').textContent = '85%';
        }

        function loadRecentDocuments() {
            const tbody = document.getElementById('recentDocumentsTable');
            
            // Sample recent documents
            const recentDocs = [
                { id: 1, name: 'Strategic Plan Review 2024.pdf', type: 'PDF', date: '2024-12-15', status: 'Uploaded' },
                { id: 2, name: 'Quality Manual Update.docx', type: 'Word', date: '2024-12-14', status: 'Tagged' },
                { id: 3, name: 'Faculty Evaluation Form.xlsx', type: 'Excel', date: '2024-12-13', status: 'Organized' },
                { id: 4, name: 'Curriculum Assessment.pptx', type: 'PowerPoint', date: '2024-12-12', status: 'Uploaded' }
            ];
            
            tbody.innerHTML = recentDocs.map(doc => `
                <tr>
                    <td><strong>${doc.name}</strong></td>
                    <td><span class="badge bg-secondary">${doc.type}</span></td>
                    <td>${formatDate(doc.date)}</td>
                    <td><span class="badge ${getStatusBadgeClass(doc.status)}">${doc.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="viewDocument(${doc.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success me-1" onclick="editTags(${doc.id})">
                            <i class="fas fa-tags"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="organizeDocument(${doc.id})">
                            <i class="fas fa-folder"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function loadDocumentCategories() {
            const container = document.getElementById('documentCategories');
            const categories = [
                { name: 'Policies & Procedures', count: 12, color: 'primary' },
                { name: 'Reports & Assessments', count: 8, color: 'success' },
                { name: 'Supporting Evidence', count: 15, color: 'info' },
                { name: 'Forms & Templates', count: 6, color: 'warning' }
            ];
            
            container.innerHTML = categories.map(cat => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-${cat.color}">${cat.name}</span>
                    <small class="text-muted">${cat.count} docs</small>
                </div>
            `).join('');
        }

        function setupFormHandlers() {
            // Quick upload form
            document.getElementById('quickUploadForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const files = document.getElementById('documentFile').files;
                if (files.length > 0) {
                    simulateUpload(files.length);
                } else {
                    showToast('Please select files to upload', 'warning');
                }
            });
        }

        function getStatusBadgeClass(status) {
            switch(status) {
                case 'Uploaded': return 'bg-primary';
                case 'Tagged': return 'bg-success';
                case 'Organized': return 'bg-info';
                default: return 'bg-secondary';
            }
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        // Action functions
        function openUploadModal() {
            const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
            modal.show();
        }

        function uploadDocuments() {
            const files = document.getElementById('modalDocumentFiles').files;
            const category = document.getElementById('documentCategory').value;
            const tags = document.getElementById('documentTags').value;
            const description = document.getElementById('documentDescription').value;
            
            if (files.length === 0) {
                showToast('Please select files to upload', 'warning');
                return;
            }
            
            simulateUpload(files.length);
            const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
            modal.hide();
        }

        function simulateUpload(fileCount) {
            showToast(`Uploading ${fileCount} document(s)...`, 'info');
            
            setTimeout(() => {
                showToast(`${fileCount} document(s) uploaded successfully!`, 'success');
                loadRecentDocuments();
                loadStatistics();
            }, 2000);
        }

        function organizeDocuments() {
            showToast('Opening document organization interface...', 'info');
            setTimeout(() => {
                window.location.href = 'documents.html';
            }, 1000);
        }

        function tagDocuments() {
            showToast('Opening document tagging interface...', 'info');
        }

        function viewAllDocuments() {
            window.location.href = 'documents.html';
        }

        function viewDocument(docId) {
            showToast('Opening document viewer...', 'info');
        }

        function editTags(docId) {
            const newTags = prompt('Enter new tags (comma-separated):');
            if (newTags) {
                showToast('Tags updated successfully!', 'success');
            }
        }

        function organizeDocument(docId) {
            const newCategory = prompt('Enter new category:');
            if (newCategory) {
                showToast('Document moved to new category!', 'success');
            }
        }

        // Logout functionality
        function logoutUser() {
            if (confirm('Are you sure you want to logout?')) {
                sessionStorage.removeItem('currentUser');
                showToast('Logged out successfully!', 'success');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
            }
        }
    </script>
</body>
</html> 