<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Departments & Programs - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-sitemap me-3"></i>Departments & Programs</h1>
                    <p>Manage academic departments and their associated programs for accreditation tracking</p>
                </div>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="program-list-tab" data-bs-toggle="tab" data-bs-target="#program-list" type="button" role="tab">
                        <i class="fas fa-list me-2"></i>Program List
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="accreditation-status-tab" data-bs-toggle="tab" data-bs-target="#accreditation-status" type="button" role="tab">
                        <i class="fas fa-certificate me-2"></i>Accreditation Status
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="coordinators-tab" data-bs-toggle="tab" data-bs-target="#coordinators" type="button" role="tab">
                        <i class="fas fa-user-tie me-2"></i>Assigned Coordinators
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Program List Tab -->
                <div class="tab-pane fade show active" id="program-list" role="tabpanel">
                    <!-- Search and Filter Bar -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchPrograms" placeholder="Search programs..." onkeyup="searchTable(this.value, 'programsTableBody')">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterProgramDepartment" onchange="filterProgramsByDepartment(this.value)">
                                    <option value="all">All Departments</option>
                                    <!-- Options will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="filterProgramStatus" onchange="filterByStatus(this.value, 'programsTableBody')">
                                    <option value="all">All Status</option>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-nd-green" onclick="openAddProgramModal()">
                                    <i class="fas fa-plus me-2"></i>Add Program
                                </button>
                                <button class="btn btn-outline-secondary" onclick="exportPrograms()">
                                    <i class="fas fa-download me-2"></i>Export
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Programs Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Programs List</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Program Code</th>
                                            <th>Program Name</th>
                                            <th>Department</th>
                                            <th>Status</th>
                                            <th>Accreditation Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="programsTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Accreditation Status Tab -->
                <div class="tab-pane fade" id="accreditation-status" role="tabpanel">
                    <!-- Status Filter -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <select class="form-select" id="statusFilter" onchange="filterAccreditationStatus()">
                                    <option value="all">All Status</option>
                                    <option value="Not Started">Not Started</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Under Review">Under Review</option>
                                    <option value="Accredited">Accredited</option>
                                    <option value="Conditional">Conditional</option>
                                    <option value="Expired">Expired</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="agencyFilter" onchange="filterAccreditationStatus()">
                                    <option value="all">All Agencies</option>
                                    <option value="PAASCU">PAASCU</option>
                                    <option value="AACCUP">AACCUP</option>
                                    <option value="PACUCOA">PACUCOA</option>
                                </select>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-primary" onclick="openUpdateStatusModal()">
                                    <i class="fas fa-edit me-2"></i>Update Status
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Accreditation Status Cards -->
                    <div class="row" id="accreditationStatusContainer">
                        <!-- Status cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Assigned Coordinators Tab -->
                <div class="tab-pane fade" id="coordinators" role="tabpanel">
                    <!-- Coordinator Actions -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchCoordinators" placeholder="Search coordinators..." onkeyup="searchCoordinators()">
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-nd-green" onclick="openAssignCoordinatorModal()">
                                    <i class="fas fa-user-plus me-2"></i>Assign Coordinator
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Coordinators Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-user-tie me-2"></i>Program Coordinators</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Coordinator Name</th>
                                            <th>Email</th>
                                            <th>Department</th>
                                            <th>Assigned Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="coordinatorsTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Department Modal -->
    <div class="modal fade" id="addDepartmentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add Department</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addDepartmentForm">
                        <div class="mb-3">
                            <label for="departmentCode" class="form-label">Department Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="departmentCode" required>
                        </div>
                        <div class="mb-3">
                            <label for="departmentName" class="form-label">Department Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="departmentName" required>
                        </div>
                        <div class="mb-3">
                            <label for="departmentStatus" class="form-label">Status</label>
                            <select class="form-select" id="departmentStatus">
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveDepartment()">Save Department</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Program Modal -->
    <div class="modal fade" id="addProgramModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add Program</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProgramForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="programCode" class="form-label">Program Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="programCode" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="programFullName" class="form-label">Program Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="programFullName" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="programDepartment" class="form-label">Department <span class="text-danger">*</span></label>
                                    <select class="form-select" id="programDepartment" required>
                                        <option value="">Select Department</option>
                                        <!-- Options will be populated by JavaScript -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="programLevel" class="form-label">Academic Level</label>
                                    <select class="form-select" id="programLevel">
                                        <option value="Undergraduate">Undergraduate</option>
                                        <option value="Graduate">Graduate</option>
                                        <option value="Doctorate">Doctorate</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="programStatus" class="form-label">Status</label>
                                    <select class="form-select" id="programStatus">
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="programYearEstablished" class="form-label">Year Established</label>
                                    <input type="number" class="form-control" id="programYearEstablished" min="1900" max="2024">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="programStudents" class="form-label">Number of Students</label>
                                    <input type="number" class="form-control" id="programStudents" min="0" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="programFaculty" class="form-label">Number of Faculty</label>
                                    <input type="number" class="form-control" id="programFaculty" min="0" value="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="programDescription" class="form-label">Program Description</label>
                                    <textarea class="form-control" id="programDescription" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveProgram()">Save Program</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Department Modal -->
    <div class="modal fade" id="editDepartmentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Department</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editDepartmentForm">
                        <input type="hidden" id="editDepartmentId">
                        <div class="mb-3">
                            <label for="editDepartmentCode" class="form-label">Department Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editDepartmentCode" required>
                        </div>
                        <div class="mb-3">
                            <label for="editDepartmentName" class="form-label">Department Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editDepartmentName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editDepartmentStatus" class="form-label">Status</label>
                            <select class="form-select" id="editDepartmentStatus">
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateDepartment()">Update Department</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Program Modal -->
    <div class="modal fade" id="editProgramModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Program</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editProgramForm">
                        <input type="hidden" id="editProgramId">
                        <div class="mb-3">
                            <label for="editProgramCode" class="form-label">Program Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editProgramCode" required>
                        </div>
                        <div class="mb-3">
                            <label for="editProgramFullName" class="form-label">Program Full Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editProgramFullName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editProgramDepartment" class="form-label">Department <span class="text-danger">*</span></label>
                            <select class="form-select" id="editProgramDepartment" required>
                                <option value="">Select Department</option>
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editProgramStatus" class="form-label">Status</label>
                            <select class="form-select" id="editProgramStatus">
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateProgram()">Update Program</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Accreditation Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Update Accreditation Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="updateStatusForm">
                        <div class="mb-3">
                            <label for="statusProgram" class="form-label">Program <span class="text-danger">*</span></label>
                            <select class="form-select" id="statusProgram" required>
                                <option value="">Select Program</option>
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="accreditationAgency" class="form-label">Accrediting Agency <span class="text-danger">*</span></label>
                            <select class="form-select" id="accreditationAgency" required>
                                <option value="">Select Agency</option>
                                <option value="PAASCU">PAASCU</option>
                                <option value="AACCUP">AACCUP</option>
                                <option value="PACUCOA">PACUCOA</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="accreditationStatus" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="accreditationStatus" required>
                                <option value="">Select Status</option>
                                <option value="Not Started">Not Started</option>
                                <option value="In Progress">In Progress</option>
                                <option value="Under Review">Under Review</option>
                                <option value="Accredited">Accredited</option>
                                <option value="Conditional">Conditional</option>
                                <option value="Expired">Expired</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="statusDate" class="form-label">Status Date</label>
                            <input type="date" class="form-control" id="statusDate">
                        </div>
                        <div class="mb-3">
                            <label for="statusNotes" class="form-label">Notes</label>
                            <textarea class="form-control" id="statusNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateAccreditationStatus()">Update Status</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Program Details Modal -->
    <div class="modal fade" id="programDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>Program Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="programDetailsContent">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="editProgramFromDetails()">
                        <i class="fas fa-edit me-2"></i>Edit Program
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Assign Coordinator Modal -->
    <div class="modal fade" id="assignCoordinatorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-plus me-2"></i>Assign Coordinator</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="assignCoordinatorForm">
                        <div class="mb-3">
                            <label for="coordinatorProgram" class="form-label">Program <span class="text-danger">*</span></label>
                            <select class="form-select" id="coordinatorProgram" required>
                                <option value="">Select Program</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="coordinatorName" class="form-label">Coordinator Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="coordinatorName" required>
                        </div>
                        <div class="mb-3">
                            <label for="coordinatorEmail" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="coordinatorEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="coordinatorRole" class="form-label">Role</label>
                            <select class="form-select" id="coordinatorRole">
                                <option value="Program Coordinator">Program Coordinator</option>
                                <option value="Assistant Coordinator">Assistant Coordinator</option>
                                <option value="Department Head">Department Head</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="coordinatorStatus" class="form-label">Status</label>
                            <select class="form-select" id="coordinatorStatus">
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="assignCoordinator()">Assign Coordinator</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Accreditation Status Modal -->
    <div class="modal fade" id="editAccreditationStatusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Accreditation Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editAccreditationStatusForm">
                        <input type="hidden" id="editAccreditationStatusId">
                        <div class="mb-3">
                            <label for="editStatusProgram" class="form-label">Program <span class="text-danger">*</span></label>
                            <select class="form-select" id="editStatusProgram" required>
                                <option value="">Select Program</option>
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editAccreditationAgency" class="form-label">Accrediting Agency <span class="text-danger">*</span></label>
                            <select class="form-select" id="editAccreditationAgency" required>
                                <option value="">Select Agency</option>
                                <option value="PAASCU">PAASCU</option>
                                <option value="AACCUP">AACCUP</option>
                                <option value="PACUCOA">PACUCOA</option>
                                <option value="ACUCA">ACUCA</option>
                                <option value="AAAP">AAAP</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editAccreditationStatusSelect" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="editAccreditationStatusSelect" required>
                                <option value="">Select Status</option>
                                <option value="Not Started">Not Started</option>
                                <option value="In Progress">In Progress</option>
                                <option value="Under Review">Under Review</option>
                                <option value="Accredited">Accredited</option>
                                <option value="Conditional">Conditional</option>
                                <option value="Expired">Expired</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editStatusDate" class="form-label">Status Date</label>
                            <input type="date" class="form-control" id="editStatusDate">
                        </div>
                        <div class="mb-3">
                            <label for="editValidUntil" class="form-label">Valid Until</label>
                            <input type="date" class="form-control" id="editValidUntil">
                        </div>
                        <div class="mb-3">
                            <label for="editStatusNotes" class="form-label">Notes</label>
                            <textarea class="form-control" id="editStatusNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateEditedAccreditationStatus()">Update Status</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Page-specific JavaScript for Programs
        let currentEditingProgramId = null;
        let accreditationStatuses = [];
        let programCoordinators = [];

        // Initialize page on load
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize centralized data system
            initializePage();
            
            // Load centralized data
            loadAllDataFromStorage();
            
            // Initialize page-specific data
            initializeProgramData();
            
            // Load all displays
            loadPrograms();
            loadAccreditationStatuses();
            loadProgramCoordinators();
            populateDepartmentDropdowns();
        });

        // Initialize program-specific data
        function initializeProgramData() {
            // Initialize accreditation statuses if empty
            if (accreditationStatuses.length === 0) {
                accreditationStatuses = [
                    { id: 1, programId: 1, agency: 'PAASCU', status: 'Accredited', statusDate: '2023-05-15', validUntil: '2028-05-14', notes: 'Level II Accreditation' },
                    { id: 2, programId: 4, agency: 'AACCUP', status: 'In Progress', statusDate: '2024-01-10', validUntil: null, notes: 'Initial accreditation in progress' },
                    { id: 3, programId: 9, agency: 'PAASCU', status: 'Accredited', statusDate: '2022-11-20', validUntil: '2027-11-19', notes: 'Level II Accreditation maintained' }
                ];
            }
            
            // Initialize program coordinators if empty
            if (programCoordinators.length === 0) {
                programCoordinators = [
                    { id: 1, programId: 1, name: 'Dr. Alice Johnson', email: '<EMAIL>', role: 'Program Coordinator', department: 1, assignedDate: '2023-01-15', status: 'Active' },
                    { id: 2, programId: 4, name: 'Prof. Mark Davis', email: '<EMAIL>', role: 'Program Coordinator', department: 2, assignedDate: '2023-03-10', status: 'Active' },
                    { id: 3, programId: 9, name: 'Dr. Sarah Brown', email: '<EMAIL>', role: 'Program Coordinator', department: 4, assignedDate: '2022-09-05', status: 'Active' }
                ];
            }
        }

        // Load programs
        function loadPrograms() {
            const tableBody = document.getElementById('programsTableBody');
            tableBody.innerHTML = '';

            programs.forEach(program => {
                const dept = getEntityById(departments, program.departmentId);
                
                // Find actual accreditation status (don't default to "Accredited")
                const accreditationStatus = accreditationStatuses.find(s => s.programId === program.id);
                let statusBadge = '<span class="badge bg-secondary">Not Started</span>';
                
                if (accreditationStatus) {
                    statusBadge = `<span class="badge ${getAccreditationStatusClass(accreditationStatus.status)}">${accreditationStatus.status}</span>`;
                }

                const row = `
                    <tr onclick="viewProgramDetails(${program.id})" style="cursor: pointer;">
                        <td>${program.id}</td>
                        <td><strong>${program.code || program.name}</strong></td>
                        <td>${program.name}</td>
                        <td>${dept ? dept.name : 'N/A'}</td>
                        <td class="status-cell">
                            <span class="badge ${program.status === 'Active' ? 'bg-success' : 'bg-danger'}">${program.status}</span>
                        </td>
                        <td>${statusBadge}</td>
                        <td onclick="event.stopPropagation();">
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-primary" onclick="editProgram(${program.id})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProgram(${program.id})">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        // Get accreditation status class
        function getAccreditationStatusClass(status) {
            switch(status) {
                case 'Accredited': return 'bg-success';
                case 'In Progress': return 'bg-warning';
                case 'Under Review': return 'bg-info';
                case 'Conditional': return 'bg-warning';
                case 'Expired': return 'bg-danger';
                case 'Not Started': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        // Load accreditation statuses
        function loadAccreditationStatuses() {
            const container = document.getElementById('accreditationStatusContainer');
            container.innerHTML = '';

            const statusGroups = {};
            accreditationStatuses.forEach(status => {
                if (!statusGroups[status.status]) {
                    statusGroups[status.status] = [];
                }
                statusGroups[status.status].push(status);
            });

            Object.keys(statusGroups).forEach(statusName => {
                const programs = statusGroups[statusName];
                const card = `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header ${getAccreditationStatusClass(statusName)} text-white">
                                <h6 class="card-title mb-0">${statusName} (${programs.length})</h6>
                            </div>
                            <div class="card-body">
                                ${programs.map(status => {
                                    const program = getEntityById(programs, status.programId);
                                    return `
                                        <div class="border-bottom py-2 mb-2">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <strong>${program ? program.name : 'Unknown'}</strong><br>
                                                    <small class="text-muted">Agency: ${status.agency}</small><br>
                                                    ${status.statusDate ? `<small class="text-muted">Date: ${formatDate(status.statusDate)}</small>` : ''}
                                                    ${status.notes ? `<br><small class="text-muted">${status.notes}</small>` : ''}
                                                </div>
                                                <div class="action-buttons ms-2">
                                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editAccreditationStatus(${status.id})" title="Edit Status">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteAccreditationStatus(${status.id})" title="Delete Status">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += card;
            });
        }

        // Load program coordinators
        function loadProgramCoordinators() {
            const tableBody = document.getElementById('coordinatorsTableBody');
            tableBody.innerHTML = '';

            programCoordinators.forEach(coordinator => {
                const program = getEntityById(programs, coordinator.programId);
                const row = `
                    <tr>
                        <td><strong>${program ? program.name : 'Unknown Program'}</strong></td>
                        <td>${coordinator.name}</td>
                        <td>${coordinator.email}</td>
                        <td>${coordinator.department}</td>
                        <td>${formatDate(coordinator.assignedDate)}</td>
                        <td>
                            <span class="badge ${coordinator.status === 'Active' ? 'bg-success' : 'bg-danger'}">${coordinator.status}</span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-primary" onclick="editCoordinator(${coordinator.id})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="removeCoordinator(${coordinator.id})">
                                    <i class="fas fa-trash"></i> Remove
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        // Open update status modal
        function openUpdateStatusModal() {
            populateSelect('statusProgram', programs, 'name');
            openModal('updateStatusModal');
        }

        // Update accreditation status
        function updateAccreditationStatus() {
            if (!validateForm('updateStatusForm')) return;

            const programId = parseInt(document.getElementById('statusProgram').value);
            const agency = document.getElementById('accreditationAgency').value;
            const status = document.getElementById('accreditationStatus').value;
            const statusDate = document.getElementById('statusDate').value;
            const notes = document.getElementById('statusNotes').value;

            // Check if status already exists
            const existingIndex = accreditationStatuses.findIndex(s => s.programId === programId && s.agency === agency);
            
            if (existingIndex >= 0) {
                accreditationStatuses[existingIndex] = {
                    ...accreditationStatuses[existingIndex],
                    status, statusDate, notes
                };
                
                // Add activity log entry for tracking
                const program = getEntityById(programs, programId);
                addActivityLog('Accreditation Status Updated', `Updated ${agency} accreditation status for ${program?.name || 'Unknown Program'} to ${status}`, 'accreditation', accreditationStatuses[existingIndex].id);
            } else {
                const newStatus = {
                    id: generateId(accreditationStatuses),
                    programId, agency, status, statusDate, notes,
                    validUntil: null
                };
                accreditationStatuses.push(newStatus);
                
                // Add activity log entry for tracking
                const program = getEntityById(programs, programId);
                addActivityLog('Accreditation Status Created', `Set ${agency} accreditation status for ${program?.name || 'Unknown Program'} to ${status}`, 'accreditation', newStatus.id);
            }

            // Save to centralized storage
            saveAllDataToStorage();

            loadPrograms();
            loadAccreditationStatuses();
            closeModal('updateStatusModal');
            showToast('Accreditation status updated successfully! This change is now visible to all administrators.', 'success');
        }

        // Edit accreditation status
        function editAccreditationStatus(id) {
            const status = getEntityById(accreditationStatuses, id);
            if (!status) return;

            // Populate the edit form
            document.getElementById('editAccreditationStatusId').value = status.id;
            document.getElementById('editStatusProgram').value = status.programId;
            document.getElementById('editAccreditationAgency').value = status.agency;
            document.getElementById('editAccreditationStatusSelect').value = status.status;
            document.getElementById('editStatusDate').value = status.statusDate || '';
            document.getElementById('editValidUntil').value = status.validUntil || '';
            document.getElementById('editStatusNotes').value = status.notes || '';

            // Populate program dropdown for edit form
            populateSelect('editStatusProgram', programs, 'name');
            document.getElementById('editStatusProgram').value = status.programId;

            // Open edit modal
            openModal('editAccreditationStatusModal');
        }

        // Update edited accreditation status
        function updateEditedAccreditationStatus() {
            if (!validateForm('editAccreditationStatusForm')) return;

            const statusId = parseInt(document.getElementById('editAccreditationStatusId').value);
            const statusIndex = accreditationStatuses.findIndex(s => s.id === statusId);
            
            if (statusIndex === -1) {
                showToast('Error: Accreditation status not found.', 'error');
                return;
            }

            const oldStatus = { ...accreditationStatuses[statusIndex] };
            accreditationStatuses[statusIndex] = {
                ...accreditationStatuses[statusIndex],
                programId: parseInt(document.getElementById('editStatusProgram').value),
                agency: document.getElementById('editAccreditationAgency').value,
                status: document.getElementById('editAccreditationStatusSelect').value,
                statusDate: document.getElementById('editStatusDate').value,
                validUntil: document.getElementById('editValidUntil').value,
                notes: document.getElementById('editStatusNotes').value
            };

            // Add activity log entry for tracking
            const program = getEntityById(programs, accreditationStatuses[statusIndex].programId);
            addActivityLog('Accreditation Status Edited', `Edited ${accreditationStatuses[statusIndex].agency} accreditation status for ${program?.name || 'Unknown Program'}`, 'accreditation', statusId);

            // Save to centralized storage
            saveAllDataToStorage();

            loadPrograms();
            loadAccreditationStatuses();
            closeModal('editAccreditationStatusModal');
            showToast('Accreditation status updated successfully! This change is now visible to all administrators.', 'success');
        }

        // Delete accreditation status
        function deleteAccreditationStatus(id) {
            const status = getEntityById(accreditationStatuses, id);
            if (!status) return;

            const program = getEntityById(programs, status.programId);
            const confirmMessage = `Are you sure you want to delete the ${status.agency} accreditation status for ${program?.name || 'Unknown Program'}? This action cannot be undone.`;
            
            if (confirm(confirmMessage)) {
                // Remove from accreditation statuses array
                accreditationStatuses = accreditationStatuses.filter(s => s.id !== id);
                
                // Add activity log entry for tracking
                addActivityLog('Accreditation Status Deleted', `Deleted ${status.agency} accreditation status for ${program?.name || 'Unknown Program'}`, 'accreditation', id);
                
                // Save to centralized storage
                saveAllDataToStorage();
                
                loadPrograms();
                loadAccreditationStatuses();
                showToast('Accreditation status deleted successfully! This change is now visible to all administrators.', 'success');
            }
        }

        // Open assign coordinator modal
        function openAssignCoordinatorModal() {
            populateSelect('coordinatorProgram', programs, 'name');
            openModal('assignCoordinatorModal');
        }

        // Assign coordinator
        function assignCoordinator() {
            if (!validateForm('assignCoordinatorForm')) return;

            const newCoordinator = {
                id: generateId(programCoordinators),
                programId: parseInt(document.getElementById('coordinatorProgram').value),
                name: document.getElementById('coordinatorName').value,
                email: document.getElementById('coordinatorEmail').value,
                role: document.getElementById('coordinatorRole').value,
                department: getEntityById(programs, parseInt(document.getElementById('coordinatorProgram').value))?.departmentId || 'N/A',
                assignedDate: new Date().toISOString().split('T')[0],
                status: document.getElementById('coordinatorStatus').value
            };

            programCoordinators.push(newCoordinator);
            
            // Add activity log entry for tracking
            const program = getEntityById(programs, newCoordinator.programId);
            addActivityLog('Coordinator Assigned', `Assigned ${newCoordinator.name} as coordinator for ${program?.name || 'Unknown Program'}`, 'coordinator', newCoordinator.id);
            
            // Save to centralized storage
            saveAllDataToStorage();
            
            loadProgramCoordinators();
            closeModal('assignCoordinatorModal');
            showToast('Coordinator assigned successfully! This change is now visible to all administrators.', 'success');
        }

        // Helper function to populate select dropdowns
        function populateSelect(selectId, data, textField) {
            const select = document.getElementById(selectId);
            const currentValue = select.value;
            select.innerHTML = '<option value="">Select...</option>';
            
            data.forEach(item => {
                select.innerHTML += `<option value="${item.id}">${item[textField]}</option>`;
            });
            
            if (currentValue) select.value = currentValue;
        }

        // Existing functions (keeping the ones that are still needed)
        function populateDepartmentDropdowns() {
            const selects = ['filterProgramDepartment'];
            
            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const currentValue = select.value;
                    
                    if (selectId === 'filterProgramDepartment') {
                        select.innerHTML = '<option value="all">All Departments</option>';
                    } else {
                        select.innerHTML = '<option value="">Select Department</option>';
                    }
                    
                    departments.forEach(dept => {
                        if (dept.status === 'Active' || selectId === 'filterProgramDepartment') {
                            const option = document.createElement('option');
                            option.value = dept.id;
                            option.textContent = dept.name;
                            select.appendChild(option);
                        }
                    });
                    
                    if (currentValue) {
                        select.value = currentValue;
                    }
                }
            });
        }

        // Filter functions
        function filterProgramsByDepartment(departmentId) {
            const tableBody = document.getElementById('programsTableBody');
            const rows = tableBody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                if (departmentId === 'all') {
                    row.style.display = '';
                } else {
                    const program = programs[i];
                    if (program && program.departmentId == departmentId) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            }
        }

        function filterAccreditationStatus() {
            const statusFilter = document.getElementById('statusFilter').value;
            const agencyFilter = document.getElementById('agencyFilter').value;
            
            loadAccreditationStatuses(); // Reload with current filters
        }

        function searchCoordinators() {
            const searchTerm = document.getElementById('searchCoordinators').value.toLowerCase();
            const tableBody = document.getElementById('coordinatorsTableBody');
            const rows = tableBody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            }
        }

        // Additional helper functions
        function editProgram(id) {
            const program = getEntityById(programs, id);
            if (!program) return;

            currentEditingProgramId = id;

            // Populate edit form
            document.getElementById('editProgramId').value = program.id;
            document.getElementById('editProgramCode').value = program.name;
            document.getElementById('editProgramFullName').value = program.fullName;
            document.getElementById('editProgramDepartment').value = program.departmentId;
            document.getElementById('editProgramStatus').value = program.status;

            // Populate department dropdown for edit form
            populateEditDepartmentDropdown();
            
            // Open edit modal
            openModal('editProgramModal');
        }

        function deleteProgram(id) {
            if (confirm('Are you sure you want to delete this program? This action cannot be undone.')) {
                const program = getEntityById(programs, id);
                
                // Remove from programs array
                programs = programs.filter(p => p.id !== id);
                
                // Add activity log entry for tracking
                if (program) {
                    addActivityLog('Program Deleted', `Deleted program: ${program.name}`, 'program', id);
                }
                
                // Save to centralized storage
                saveAllDataToStorage();
                
                loadPrograms();
                showToast('Program deleted successfully! This change is now visible to all administrators.', 'success');
            }
        }

        function editCoordinator(id) {
            showToast('Edit coordinator functionality - to be implemented');
        }

        function removeCoordinator(id) {
            if (confirm('Are you sure you want to remove this coordinator?')) {
                programCoordinators = programCoordinators.filter(c => c.id !== id);
                loadProgramCoordinators();
                showToast('Coordinator removed successfully!');
            }
        }

        function exportPrograms() {
            const exportData = programs.map(program => {
                const dept = getEntityById(departments, program.departmentId);
                return {
                    id: program.id,
                    name: program.name,
                    fullName: program.fullName,
                    department: dept ? dept.name : 'N/A',
                    status: program.status
                };
            });
            exportToCSV(exportData, 'programs.csv');
        }

        // Open Add Program Modal
        function openAddProgramModal() {
            populateModalDepartmentDropdown();
            openModal('addProgramModal');
        }

        // Save Program (from modal)
        function saveProgram() {
            if (!validateForm('addProgramForm')) return;

            const newProgram = {
                id: generateId(programs),
                name: document.getElementById('programFullName').value, // Full name as main name
                fullName: document.getElementById('programFullName').value, // Also store as fullName for consistency
                code: document.getElementById('programCode').value, // Code as separate field
                departmentId: parseInt(document.getElementById('programDepartment').value),
                level: document.getElementById('programLevel').value || 'Undergraduate',
                status: document.getElementById('programStatus').value || 'Active',
                students: parseInt(document.getElementById('programStudents').value) || 0,
                faculty: parseInt(document.getElementById('programFaculty').value) || 0,
                yearEstablished: document.getElementById('programYearEstablished').value,
                description: document.getElementById('programDescription').value
            };

            // Add to programs array
            programs.push(newProgram);
            
            // Add activity log entry for tracking
            addActivityLog('Program Created', `Created new program: ${newProgram.name}`, 'program', newProgram.id);
            
            // Save to centralized storage
            saveAllDataToStorage();
            
            // Refresh displays
            loadPrograms();
            closeModal('addProgramModal');
            resetForm('addProgramForm');
            showToast('Program added successfully! This change is now visible to all administrators.', 'success');
        }

        // Update Program
        function updateProgram() {
            if (!validateForm('editProgramForm')) return;

            const programIndex = programs.findIndex(p => p.id === currentEditingProgramId);
            if (programIndex === -1) return;

            const oldProgram = { ...programs[programIndex] };
            programs[programIndex] = {
                ...programs[programIndex],
                code: document.getElementById('editProgramCode').value,
                name: document.getElementById('editProgramFullName').value,
                departmentId: parseInt(document.getElementById('editProgramDepartment').value),
                status: document.getElementById('editProgramStatus').value
            };

            // Add activity log entry for tracking
            addActivityLog('Program Updated', `Updated program: ${programs[programIndex].name}`, 'program', currentEditingProgramId);
            
            // Save to centralized storage
            saveAllDataToStorage();

            loadPrograms();
            closeModal('editProgramModal');
            showToast('Program updated successfully! This change is now visible to all administrators.', 'success');
        }

        // Save Department with tracking
        function saveDepartment() {
            if (!validateForm('addDepartmentForm')) return;

            const newDepartment = {
                id: generateId(departments),
                code: document.getElementById('departmentCode').value,
                name: document.getElementById('departmentName').value,
                status: document.getElementById('departmentStatus').value,
                dean: 'TBD', // To be assigned
                programs: 0,
                established: new Date().getFullYear().toString()
            };

            departments.push(newDepartment);
            
            // Add activity log entry for tracking
            addActivityLog('Department Created', `Created new department: ${newDepartment.name}`, 'department', newDepartment.id);
            
            // Save to centralized storage
            saveAllDataToStorage();
            
            populateDepartmentDropdowns();
            closeModal('addDepartmentModal');
            resetForm('addDepartmentForm');
            showToast('Department added successfully! This change is now visible to all administrators.', 'success');
        }

        // Update Department with tracking
        function updateDepartment() {
            if (!validateForm('editDepartmentForm')) return;

            const departmentId = parseInt(document.getElementById('editDepartmentId').value);
            const departmentIndex = departments.findIndex(d => d.id === departmentId);
            if (departmentIndex === -1) return;

            departments[departmentIndex] = {
                ...departments[departmentIndex],
                code: document.getElementById('editDepartmentCode').value,
                name: document.getElementById('editDepartmentName').value,
                status: document.getElementById('editDepartmentStatus').value
            };

            // Add activity log entry for tracking
            addActivityLog('Department Updated', `Updated department: ${departments[departmentIndex].name}`, 'department', departmentId);
            
            // Save to centralized storage
            saveAllDataToStorage();

            populateDepartmentDropdowns();
            closeModal('editDepartmentModal');
            showToast('Department updated successfully! This change is now visible to all administrators.', 'success');
        }

        // Populate department dropdown for modals
        function populateModalDepartmentDropdown() {
            const select = document.getElementById('programDepartment');
            select.innerHTML = '<option value="">Select Department</option>';
            
            departments.forEach(dept => {
                if (dept.status === 'Active') {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    select.appendChild(option);
                }
            });
        }

        // Populate department dropdown for edit modal
        function populateEditDepartmentDropdown() {
            const select = document.getElementById('editProgramDepartment');
            select.innerHTML = '<option value="">Select Department</option>';
            
            departments.forEach(dept => {
                if (dept.status === 'Active') {
                    const option = document.createElement('option');
                    option.value = dept.id;
                    option.textContent = dept.name;
                    select.appendChild(option);
                }
            });
        }

        // View Program Details
        function viewProgramDetails(id) {
            const program = getEntityById(programs, id);
            if (!program) return;

            const dept = getEntityById(departments, program.departmentId);
            const accreditationStatus = accreditationStatuses.find(s => s.programId === program.id);
            const coordinator = programCoordinators.find(c => c.programId === program.id);

            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Basic Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Program ID:</strong></td><td>${program.id}</td></tr>
                            <tr><td><strong>Program Code:</strong></td><td>${program.name}</td></tr>
                            <tr><td><strong>Full Name:</strong></td><td>${program.fullName}</td></tr>
                            <tr><td><strong>Department:</strong></td><td>${dept ? dept.name : 'N/A'}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge ${program.status === 'Active' ? 'bg-success' : 'bg-danger'}">${program.status}</span></td></tr>
                            ${program.level ? `<tr><td><strong>Level:</strong></td><td>${program.level}</td></tr>` : ''}
                            ${program.yearEstablished ? `<tr><td><strong>Year Established:</strong></td><td>${program.yearEstablished}</td></tr>` : ''}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Accreditation Status</h6>
                        ${accreditationStatus ? `
                            <table class="table table-sm">
                                <tr><td><strong>Agency:</strong></td><td>${accreditationStatus.agency}</td></tr>
                                <tr><td><strong>Status:</strong></td><td><span class="badge ${getAccreditationStatusClass(accreditationStatus.status)}">${accreditationStatus.status}</span></td></tr>
                                <tr><td><strong>Status Date:</strong></td><td>${accreditationStatus.statusDate ? formatDate(accreditationStatus.statusDate) : 'N/A'}</td></tr>
                                <tr><td><strong>Valid Until:</strong></td><td>${accreditationStatus.validUntil ? formatDate(accreditationStatus.validUntil) : 'N/A'}</td></tr>
                            </table>
                            ${accreditationStatus.notes ? `<p><strong>Notes:</strong><br><small>${accreditationStatus.notes}</small></p>` : ''}
                        ` : '<p class="text-muted">No accreditation status available</p>'}
                        
                        <h6 class="text-primary mt-3">Program Coordinator</h6>
                        ${coordinator ? `
                            <table class="table table-sm">
                                <tr><td><strong>Name:</strong></td><td>${coordinator.name}</td></tr>
                                <tr><td><strong>Email:</strong></td><td>${coordinator.email}</td></tr>
                                <tr><td><strong>Role:</strong></td><td>${coordinator.role}</td></tr>
                                <tr><td><strong>Assigned Date:</strong></td><td>${formatDate(coordinator.assignedDate)}</td></tr>
                            </table>
                        ` : '<p class="text-muted">No coordinator assigned</p>'}
                    </div>
                </div>
                ${program.description ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Description</h6>
                            <p>${program.description}</p>
                        </div>
                    </div>
                ` : ''}
            `;

            document.getElementById('programDetailsContent').innerHTML = detailsHtml;
            currentEditingProgramId = id;
            openModal('programDetailsModal');
        }

        // Edit Program from Details Modal
        function editProgramFromDetails() {
            closeModal('programDetailsModal');
            setTimeout(() => {
                editProgram(currentEditingProgramId);
            }, 300);
        }
    </script>
</body>
</html> 