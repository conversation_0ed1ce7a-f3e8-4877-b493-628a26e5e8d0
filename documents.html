<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-folder me-3"></i>Document Management</h1>
                    <p>Upload, categorize, and manage accreditation documents with area mapping</p>
                </div>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="area-uploads-tab" data-bs-toggle="tab" data-bs-target="#area-uploads" type="button" role="tab">
                        <i class="fas fa-folder-plus me-2"></i>Area-based Uploads
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="evidence-repository-tab" data-bs-toggle="tab" data-bs-target="#evidence-repository" type="button" role="tab">
                        <i class="fas fa-archive me-2"></i>Evidence Repository
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="self-survey-tab" data-bs-toggle="tab" data-bs-target="#self-survey" type="button" role="tab">
                        <i class="fas fa-clipboard-list me-2"></i>Self-Survey Reports
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="evaluation-results-tab" data-bs-toggle="tab" data-bs-target="#evaluation-results" type="button" role="tab">
                        <i class="fas fa-chart-line me-2"></i>Evaluation Results
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Area-based Uploads Tab -->
                <div class="tab-pane fade show active" id="area-uploads" role="tabpanel">
                    <!-- Area Selection and Upload -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <select class="form-select" id="selectedArea" onchange="loadAreaDocuments()">
                                    <option value="">Select Area</option>
                                    <option value="Area 1">Area 1: Philosophy/Objectives</option>
                                    <option value="Area 2">Area 2: Faculty</option>
                                    <option value="Area 3">Area 3: Curriculum & Instruction</option>
                                    <option value="Area 4">Area 4: Support to Students</option>
                                    <option value="Area 5">Area 5: Research</option>
                                    <option value="Area 6">Area 6: Extension & Community Involvement</option>
                                    <option value="Area 7">Area 7: Library</option>
                                    <option value="Area 8">Area 8: Physical Plant & Facilities</option>
                                    <option value="Area 9">Area 9: Laboratories</option>
                                    <option value="Area 10">Area 10: Administration</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="areaProgram" onchange="loadAreaDocuments()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="areaStatus" onchange="loadAreaDocuments()">
                                    <option value="all">All Status</option>
                                    <option value="Complete">Complete</option>
                                    <option value="Incomplete">Incomplete</option>
                                    <option value="Under Review">Under Review</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-nd-green" onclick="openAreaUploadModal()">
                                    <i class="fas fa-plus me-2"></i>Upload to Area
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Area Document Grid -->
                    <div id="areaDocumentsContainer">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-folder-open fa-3x mb-3"></i>
                            <h5>Select an Area to view documents</h5>
                            <p>Choose an accreditation area from the dropdown above to manage area-specific documents.</p>
                        </div>
                    </div>
                </div>

                <!-- Evidence Repository Tab -->
                <div class="tab-pane fade" id="evidence-repository" role="tabpanel">
                    <!-- Repository Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchRepository" placeholder="Search repository..." onkeyup="searchRepository()">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="repositoryType" onchange="filterRepository()">
                                    <option value="all">All Types</option>
                                    <option value="Policy">Policy</option>
                                    <option value="Procedure">Procedure</option>
                                    <option value="Evidence">Evidence</option>
                                    <option value="Template">Template</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="repositoryCategory" onchange="filterRepository()">
                                    <option value="all">All Categories</option>
                                    <option value="Academic">Academic</option>
                                    <option value="Administrative">Administrative</option>
                                    <option value="Financial">Financial</option>
                                    <option value="Faculty">Faculty</option>
                                    <option value="Student">Student</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="repositoryYear" onchange="filterRepository()">
                                    <option value="all">All Years</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                    <option value="2022">2022</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-primary" onclick="openRepositoryUploadModal()">
                                    <i class="fas fa-plus me-2"></i>Add Evidence
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Repository Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-archive me-2"></i>Evidence Repository</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Document Title</th>
                                            <th>Type</th>
                                            <th>Category</th>
                                            <th>Areas</th>
                                            <th>Upload Date</th>
                                            <th>Size</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="repositoryTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Self-Survey Reports Tab -->
                <div class="tab-pane fade" id="self-survey" role="tabpanel">
                    <!-- Survey Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="surveyProgram" onchange="filterSurveys()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="surveyStatus" onchange="filterSurveys()">
                                    <option value="all">All Status</option>
                                    <option value="Draft">Draft</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Submitted">Submitted</option>
                                    <option value="Approved">Approved</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="surveyYear" onchange="filterSurveys()">
                                    <option value="all">All Years</option>
                                    <option value="2024">2024</option>
                                    <option value="2025">2025</option>
                                    <option value="2026">2026</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-success" onclick="openCreateSurveyModal()">
                                    <i class="fas fa-plus me-2"></i>Create Survey
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Survey Cards -->
                    <div class="row" id="surveyCardsContainer">
                        <!-- Survey cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Evaluation Results Tab -->
                <div class="tab-pane fade" id="evaluation-results" role="tabpanel">
                    <!-- Results Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="resultsProgram" onchange="filterResults()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="resultsEvaluator" onchange="filterResults()">
                                    <option value="all">All Evaluators</option>
                                    <option value="PAASCU">PAASCU</option>
                                    <option value="AACCUP">AACCUP</option>
                                    <option value="PACUCOA">PACUCOA</option>
                                    <option value="Internal">Internal Review</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="resultsYear" onchange="filterResults()">
                                    <option value="all">All Years</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                    <option value="2022">2022</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-warning" onclick="openAddResultModal()">
                                    <i class="fas fa-plus me-2"></i>Add Result
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Results Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Evaluation Results</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Program</th>
                                            <th>Evaluation Type</th>
                                            <th>Evaluator</th>
                                            <th>Date</th>
                                            <th>Overall Rating</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultsTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Upload Document Modal -->
    <div class="modal fade" id="uploadDocumentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-upload me-2"></i>Upload Document</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadDocumentForm">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="documentTitle" class="form-label">Document Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="documentTitle" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="documentCategory" class="form-label">Category <span class="text-danger">*</span></label>
                                    <select class="form-select" id="documentCategory" required onchange="toggleProgramSelection()">
                                        <option value="">Select Category</option>
                                        <option value="Institutional">Institutional</option>
                                        <option value="Program-Specific">Program-Specific</option>
                                        <option value="Shared">Shared</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="documentArea" class="form-label">Accreditation Area <span class="text-danger">*</span></label>
                                    <select class="form-select" id="documentArea" required onchange="updateSubAreas()">
                                        <option value="">Select Area</option>
                                        <option value="Area 1">Area 1: Philosophy/Objectives</option>
                                        <option value="Area 2">Area 2: Faculty</option>
                                        <option value="Area 3">Area 3: Curriculum & Instruction</option>
                                        <option value="Area 4">Area 4: Support to Students</option>
                                        <option value="Area 5">Area 5: Research</option>
                                        <option value="Area 6">Area 6: Extension & Community Involvement</option>
                                        <option value="Area 7">Area 7: Library</option>
                                        <option value="Area 8">Area 8: Physical Plant & Facilities</option>
                                        <option value="Area 9">Area 9: Laboratories</option>
                                        <option value="Area 10">Area 10: Administration</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="documentSubArea" class="form-label">Sub-Area <span class="text-danger">*</span></label>
                                    <select class="form-select" id="documentSubArea" required>
                                        <option value="">Select Sub-Area</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="documentStatus" class="form-label">Status</label>
                                    <select class="form-select" id="documentStatus">
                                        <option value="Draft">Draft</option>
                                        <option value="Under Review">Under Review</option>
                                        <option value="Approved">Approved</option>
                                        <option value="Rejected">Rejected</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="programSelectionRow" style="display: none;">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Applicable Programs <span class="text-danger">*</span></label>
                                    <div class="border rounded p-3">
                                        <div id="programCheckboxes">
                                            <!-- Program checkboxes will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="documentDescription" class="form-label">Description</label>
                                    <textarea class="form-control" id="documentDescription" rows="3" placeholder="Optional description or notes about the document..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="documentTags" class="form-label">Tags</label>
                                    <input type="text" class="form-control" id="documentTags" placeholder="Enter tags separated by commas (e.g., faculty, training, development)">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveDocument()">
                        <i class="fas fa-save me-2"></i>Save Document
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Document Modal -->
    <div class="modal fade" id="viewDocumentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Document Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="documentDetails">
                        <!-- Document details will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-primary" onclick="downloadDocument()">
                        <i class="fas fa-download me-2"></i>Download
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Page-specific JavaScript for Document Management
        let currentViewingDocumentId = null;
        let selectedFiles = [];
        let areaDocuments = [];
        let evidenceRepository = [];
        let selfSurveyReports = [];
        let evaluationResults = [];

        // Sub-areas mapping
        const subAreasMapping = {
            'Area 1': ['Institutional Vision-Mission', 'Program Alignment', 'Strategic Planning'],
            'Area 2': ['Hiring and Promotion', 'Faculty Development', 'Workload and Performance'],
            'Area 3': ['Curriculum Design', 'Teaching Methods', 'Assessment Strategies'],
            'Area 4': ['Student Services', 'Academic Support', 'Student Development'],
            'Area 5': ['Research Programs', 'Faculty Research', 'Student Research'],
            'Area 6': ['Local Linkages', 'International Partnerships', 'Community Engagement'],
            'Area 7': ['Library Resources', 'Information Services', 'Digital Collections'],
            'Area 8': ['Campus Facilities', 'Classroom Infrastructure', 'Safety Measures'],
            'Area 9': ['Laboratory Equipment', 'Safety Protocols', 'Maintenance'],
            'Area 10': ['Governance Structure', 'Policy Implementation', 'Quality Assurance']
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeDocumentData();
            loadAreaDocuments();
            loadEvidenceRepository();
            loadSelfSurveyReports();
            loadEvaluationResults();
            populateDropdowns();
        });

        // Initialize sample data for all tabs
        function initializeDocumentData() {
            if (areaDocuments.length === 0) {
                areaDocuments = [
                    { id: 1, title: 'Institutional Mission Statement', area: 'Area 1', subArea: 'Institutional Vision-Mission', programId: null, status: 'Complete', uploadDate: '2024-01-15', size: '2.5 MB' },
                    { id: 2, title: 'Faculty Development Plan', area: 'Area 2', subArea: 'Faculty Development', programId: 1, status: 'Complete', uploadDate: '2024-02-10', size: '5.2 MB' },
                    { id: 3, title: 'Curriculum Map - BSIT', area: 'Area 3', subArea: 'Curriculum Design', programId: 1, status: 'Under Review', uploadDate: '2024-03-05', size: '3.8 MB' },
                    { id: 4, title: 'Library Collection Report', area: 'Area 7', subArea: 'Library Resources', programId: null, status: 'Incomplete', uploadDate: '2024-02-20', size: '1.2 MB' }
                ];
            }

            if (evidenceRepository.length === 0) {
                evidenceRepository = [
                    { id: 1, title: 'Academic Policy Manual', type: 'Policy', category: 'Academic', areas: ['Area 1', 'Area 3'], uploadDate: '2024-01-10', size: '15.6 MB' },
                    { id: 2, title: 'Faculty Credentials Database', type: 'Evidence', category: 'Faculty', areas: ['Area 2'], uploadDate: '2024-02-15', size: '8.3 MB' },
                    { id: 3, title: 'Student Evaluation Forms', type: 'Template', category: 'Student', areas: ['Area 4'], uploadDate: '2024-03-01', size: '1.5 MB' },
                    { id: 4, title: 'Financial Audit Report 2023', type: 'Evidence', category: 'Financial', areas: ['Area 10'], uploadDate: '2024-01-25', size: '4.7 MB' }
                ];
            }

            if (selfSurveyReports.length === 0) {
                selfSurveyReports = [
                    { id: 1, programId: 1, title: 'BSIT Self-Survey Report 2024', status: 'Completed', year: '2024', areas: ['Area 1', 'Area 2', 'Area 3'], progress: 100, submissionDate: '2024-03-15' },
                    { id: 2, programId: 2, title: 'BSCS Self-Survey Report 2024', status: 'In Progress', year: '2024', areas: ['Area 1', 'Area 2'], progress: 65, submissionDate: null },
                    { id: 3, programId: 3, title: 'BSN Self-Survey Report 2024', status: 'Draft', year: '2024', areas: ['Area 1'], progress: 25, submissionDate: null }
                ];
            }

            if (evaluationResults.length === 0) {
                evaluationResults = [
                    { id: 1, programId: 1, evaluationType: 'Initial Accreditation', evaluator: 'PAASCU', date: '2023-11-15', overallRating: 'Level III', status: 'Final', recommendations: 5 },
                    { id: 2, programId: 2, evaluationType: 'Re-accreditation', evaluator: 'AACCUP', date: '2024-01-20', overallRating: 'Accredited', status: 'Final', recommendations: 3 },
                    { id: 3, programId: 3, evaluationType: 'Follow-up Visit', evaluator: 'PAASCU', date: '2024-02-10', overallRating: 'Satisfactory', status: 'Pending', recommendations: 2 }
                ];
            }
        }

        // Area-based Documents Functions
        function loadAreaDocuments() {
            const selectedArea = document.getElementById('selectedArea').value;
            const selectedProgram = document.getElementById('areaProgram').value;
            const selectedStatus = document.getElementById('areaStatus').value;
            const container = document.getElementById('areaDocumentsContainer');

            if (!selectedArea) {
                container.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-folder-open fa-3x mb-3"></i>
                        <h5>Select an Area to view documents</h5>
                        <p>Choose an accreditation area from the dropdown above to manage area-specific documents.</p>
                    </div>
                `;
                return;
            }

            let filteredDocs = areaDocuments.filter(doc => doc.area === selectedArea);
            
            if (selectedProgram !== 'all') {
                filteredDocs = filteredDocs.filter(doc => doc.programId == selectedProgram || doc.programId === null);
            }
            
            if (selectedStatus !== 'all') {
                filteredDocs = filteredDocs.filter(doc => doc.status === selectedStatus);
            }

            const subAreas = subAreasMapping[selectedArea] || [];
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">${selectedArea} - Sub-Areas Overview</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    ${subAreas.map(subArea => {
                                        const subAreaDocs = filteredDocs.filter(doc => doc.subArea === subArea);
                                        const completionStatus = subAreaDocs.length > 0 ? 
                                            (subAreaDocs.every(doc => doc.status === 'Complete') ? 'Complete' : 'Incomplete') : 'No Documents';
                                        return `
                                            <div class="col-md-4 mb-3">
                                                <div class="card sub-area-card ${completionStatus === 'Complete' ? 'border-success' : completionStatus === 'Incomplete' ? 'border-warning' : 'border-secondary'}">
                                                    <div class="card-body text-center">
                                                        <h6>${subArea}</h6>
                                                        <span class="badge ${completionStatus === 'Complete' ? 'bg-success' : completionStatus === 'Incomplete' ? 'bg-warning' : 'bg-secondary'}">${completionStatus}</span>
                                                        <p class="small mt-2">${subAreaDocs.length} document(s)</p>
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Documents in ${selectedArea}</h6>
                            </div>
                            <div class="card-body">
                                ${filteredDocs.length > 0 ? `
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Document Title</th>
                                                    <th>Sub-Area</th>
                                                    <th>Program</th>
                                                    <th>Status</th>
                                                    <th>Upload Date</th>
                                                    <th>Size</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${filteredDocs.map(doc => {
                                                    const program = getEntityById(programs, doc.programId);
                                                    return `
                                                        <tr>
                                                            <td><strong>${doc.title}</strong></td>
                                                            <td>${doc.subArea}</td>
                                                            <td>${program ? program.name : 'All Programs'}</td>
                                                            <td><span class="badge ${getStatusBadgeClass(doc.status)}">${doc.status}</span></td>
                                                            <td>${formatDate(doc.uploadDate)}</td>
                                                            <td>${doc.size}</td>
                                                            <td>
                                                                <button class="btn btn-sm btn-outline-primary" onclick="viewAreaDocument(${doc.id})">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteAreaDocument(${doc.id})">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    `;
                                                }).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                ` : '<p class="text-muted text-center">No documents found for the selected criteria.</p>'}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Evidence Repository Functions
        function loadEvidenceRepository() {
            const tbody = document.getElementById('repositoryTableBody');
            tbody.innerHTML = '';

            evidenceRepository.forEach(item => {
                const row = `
                    <tr>
                        <td>${item.id}</td>
                        <td><strong>${item.title}</strong></td>
                        <td><span class="badge bg-info">${item.type}</span></td>
                        <td><span class="badge bg-secondary">${item.category}</span></td>
                        <td>${item.areas.map(area => `<span class="badge bg-primary me-1">${area}</span>`).join('')}</td>
                        <td>${formatDate(item.uploadDate)}</td>
                        <td>${item.size}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewRepositoryItem(${item.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="downloadRepositoryItem(${item.id})">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteRepositoryItem(${item.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Self-Survey Reports Functions
        function loadSelfSurveyReports() {
            const container = document.getElementById('surveyCardsContainer');
            container.innerHTML = '';

            selfSurveyReports.forEach(survey => {
                const program = getEntityById(programs, survey.programId);
                const progressColor = survey.progress >= 80 ? 'success' : survey.progress >= 50 ? 'warning' : 'danger';
                
                const card = `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header ${getStatusHeaderClass(survey.status)} text-white">
                                <h6 class="card-title mb-0">${survey.title}</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Program:</strong> ${program ? program.name : 'Unknown'}</p>
                                <p><strong>Year:</strong> ${survey.year}</p>
                                <p><strong>Areas Covered:</strong></p>
                                <div class="mb-3">
                                    ${survey.areas.map(area => `<span class="badge bg-primary me-1">${area}</span>`).join('')}
                                </div>
                                <div class="mb-3">
                                    <label class="small text-muted">Progress: ${survey.progress}%</label>
                                    <div class="progress">
                                        <div class="progress-bar bg-${progressColor}" style="width: ${survey.progress}%"></div>
                                    </div>
                                </div>
                                <p><strong>Status:</strong> <span class="badge ${getStatusBadgeClass(survey.status)}">${survey.status}</span></p>
                                ${survey.submissionDate ? `<p><strong>Submitted:</strong> ${formatDate(survey.submissionDate)}</p>` : ''}
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewSurvey(${survey.id})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="editSurvey(${survey.id})">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteSurvey(${survey.id})">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += card;
            });
        }

        // Evaluation Results Functions
        function loadEvaluationResults() {
            const tbody = document.getElementById('resultsTableBody');
            tbody.innerHTML = '';

            evaluationResults.forEach(result => {
                const program = getEntityById(programs, result.programId);
                const row = `
                    <tr>
                        <td>${result.id}</td>
                        <td><strong>${program ? program.name : 'Unknown'}</strong></td>
                        <td>${result.evaluationType}</td>
                        <td><span class="badge bg-info">${result.evaluator}</span></td>
                        <td>${formatDate(result.date)}</td>
                        <td><span class="badge ${getRatingBadgeClass(result.overallRating)}">${result.overallRating}</span></td>
                        <td><span class="badge ${getStatusBadgeClass(result.status)}">${result.status}</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewEvaluationResult(${result.id})">
                                <i class="fas fa-eye"></i> View
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="viewRecommendations(${result.id})">
                                <i class="fas fa-list"></i> Recommendations (${result.recommendations})
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Populate dropdowns
        function populateDropdowns() {
            // Populate program dropdowns
            const programSelects = ['areaProgram', 'surveyProgram', 'resultsProgram'];
            programSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const currentValue = select.value;
                    select.innerHTML = '<option value="all">All Programs</option>';
                    programs.forEach(program => {
                        select.innerHTML += `<option value="${program.id}">${program.name}</option>`;
                    });
                    if (currentValue) select.value = currentValue;
                }
            });
        }

        // Helper functions
        function getStatusBadgeClass(status) {
            switch(status) {
                case 'Complete':
                case 'Completed': 
                case 'Approved': 
                case 'Final': return 'bg-success';
                case 'In Progress': 
                case 'Under Review': 
                case 'Pending': return 'bg-warning';
                case 'Incomplete': 
                case 'Draft': return 'bg-secondary';
                case 'Rejected': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        function getStatusHeaderClass(status) {
            switch(status) {
                case 'Completed': 
                case 'Submitted': 
                case 'Approved': return 'bg-success';
                case 'In Progress': return 'bg-warning';
                case 'Draft': return 'bg-secondary';
                default: return 'bg-info';
            }
        }

        function getRatingBadgeClass(rating) {
            if (rating.includes('Level III') || rating.includes('Accredited')) return 'bg-success';
            if (rating.includes('Level II') || rating.includes('Satisfactory')) return 'bg-info';
            if (rating.includes('Level I') || rating.includes('Conditional')) return 'bg-warning';
            return 'bg-secondary';
        }

        // Modal functions
        function openAreaUploadModal() {
            const selectedArea = document.getElementById('selectedArea').value;
            if (!selectedArea) {
                showToast('Please select an area first.', 'warning');
                return;
            }
            
            // Pre-populate the upload modal with the selected area
            document.getElementById('documentArea').value = selectedArea;
            populateSubAreas();
            populateProgramCheckboxes();
            openModal('uploadDocumentModal');
        }

        function openRepositoryUploadModal() {
            // Reset form and open upload modal
            resetForm('uploadDocumentForm');
            populateProgramCheckboxes();
            openModal('uploadDocumentModal');
        }

        function openCreateSurveyModal() {
            // Create a new survey using a simplified approach
            const programId = prompt('Enter Program ID (1-7):');
            if (!programId || isNaN(programId)) {
                showToast('Invalid Program ID', 'error');
                return;
            }

            const program = getEntityById(programs, parseInt(programId));
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const title = prompt(`Enter survey title for ${program.name}:`, `${program.name} Self-Survey Report 2024`);
            if (!title) return;

            const newSurvey = {
                id: generateId(selfSurveyReports),
                programId: parseInt(programId),
                title: title,
                status: 'Draft',
                year: '2024',
                areas: ['Area 1'], // Start with Area 1
                progress: 0,
                submissionDate: null
            };

            selfSurveyReports.push(newSurvey);
            loadSelfSurveyReports();
            showToast('Survey created successfully!');
        }

        function openAddResultModal() {
            // Create a new evaluation result using a simplified approach
            const programId = prompt('Enter Program ID (1-7):');
            if (!programId || isNaN(programId)) {
                showToast('Invalid Program ID', 'error');
                return;
            }

            const program = getEntityById(programs, parseInt(programId));
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const evaluationType = prompt('Enter evaluation type:', 'Initial Accreditation');
            if (!evaluationType) return;

            const evaluator = prompt('Enter evaluator (PAASCU/AACCUP/PACUCOA):', 'PAASCU');
            if (!evaluator) return;

            const rating = prompt('Enter overall rating:', 'Level II');
            if (!rating) return;

            const newResult = {
                id: generateId(evaluationResults),
                programId: parseInt(programId),
                evaluationType: evaluationType,
                evaluator: evaluator,
                date: new Date().toISOString().split('T')[0],
                overallRating: rating,
                status: 'Pending',
                recommendations: Math.floor(Math.random() * 5) + 1 // Random 1-5 recommendations
            };

            evaluationResults.push(newResult);
            loadEvaluationResults();
            showToast('Evaluation result added successfully!');
        }

        // Document form functions
        function toggleProgramSelection() {
            const category = document.getElementById('documentCategory').value;
            const programRow = document.getElementById('programSelectionRow');
            
            if (category === 'Program-Specific' || category === 'Shared') {
                programRow.style.display = 'block';
                populateProgramCheckboxes();
            } else {
                programRow.style.display = 'none';
            }
        }

        function populateSubAreas() {
            const selectedArea = document.getElementById('documentArea').value;
            const subAreaSelect = document.getElementById('documentSubArea');
            
            subAreaSelect.innerHTML = '<option value="">Select Sub-Area</option>';
            
            if (selectedArea && subAreasMapping[selectedArea]) {
                subAreasMapping[selectedArea].forEach(subArea => {
                    subAreaSelect.innerHTML += `<option value="${subArea}">${subArea}</option>`;
                });
            }
        }

        function populateProgramCheckboxes() {
            const container = document.getElementById('programCheckboxes');
            container.innerHTML = '';
            
            programs.forEach(program => {
                const dept = getEntityById(departments, program.departmentId);
                const checkbox = `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${program.id}" id="program${program.id}">
                        <label class="form-check-label" for="program${program.id}">
                            ${program.name} - ${program.fullName} (${dept ? dept.name : 'N/A'})
                        </label>
                    </div>
                `;
                container.innerHTML += checkbox;
            });
        }

        // Save document function
        function saveDocument() {
            if (!validateForm('uploadDocumentForm')) return;

            const category = document.getElementById('documentCategory').value;
            const selectedPrograms = [];
            
            if (category === 'Program-Specific' || category === 'Shared') {
                const checkboxes = document.querySelectorAll('#programCheckboxes input[type="checkbox"]:checked');
                checkboxes.forEach(checkbox => {
                    selectedPrograms.push(parseInt(checkbox.value));
                });
                
                if (selectedPrograms.length === 0) {
                    showToast('Please select at least one program.', 'error');
                    return;
                }
            }

            const newDocument = {
                id: generateId(areaDocuments),
                title: document.getElementById('documentTitle').value,
                category: category,
                area: document.getElementById('documentArea').value,
                subArea: document.getElementById('documentSubArea').value,
                programIds: selectedPrograms,
                status: document.getElementById('documentStatus').value,
                description: document.getElementById('documentDescription').value,
                tags: document.getElementById('documentTags').value,
                uploadDate: new Date().toISOString().split('T')[0],
                size: '2.5 MB' // Simulated file size
            };

            // Add to appropriate data array based on context
            if (document.getElementById('selectedArea').value) {
                // Adding to area documents
                areaDocuments.push(newDocument);
                loadAreaDocuments();
            } else {
                // Adding to evidence repository
                const repositoryItem = {
                    id: generateId(evidenceRepository),
                    title: newDocument.title,
                    type: 'Document',
                    category: category === 'Institutional' ? 'Administrative' : 'Academic',
                    areas: [newDocument.area],
                    uploadDate: newDocument.uploadDate,
                    size: newDocument.size,
                    description: newDocument.description,
                    tags: newDocument.tags
                };
                evidenceRepository.push(repositoryItem);
                loadEvidenceRepository();
            }

            closeModal('uploadDocumentModal');
            resetForm('uploadDocumentForm');
            showToast('Document uploaded successfully!');
        }

        // View document functions
        function viewAreaDocument(id) {
            const document = areaDocuments.find(doc => doc.id === id);
            if (!document) return;

            currentViewingDocumentId = id;
            showDocumentDetails(document);
            updateModalFooter('document');
            openModal('viewDocumentModal');
        }

        function viewRepositoryItem(id) {
            const item = evidenceRepository.find(item => item.id === id);
            if (!item) return;

            currentViewingDocumentId = id;
            showRepositoryItemDetails(item);
            updateModalFooter('document');
            openModal('viewDocumentModal');
        }

        function showDocumentDetails(document) {
            const program = getEntityById(programs, document.programId);
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Document Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Title:</strong></td><td>${document.title}</td></tr>
                            <tr><td><strong>Area:</strong></td><td>${document.area}</td></tr>
                            <tr><td><strong>Sub-Area:</strong></td><td>${document.subArea}</td></tr>
                            <tr><td><strong>Program:</strong></td><td>${program ? program.name : 'All Programs'}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(document.status)}">${document.status}</span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Upload Details</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Upload Date:</strong></td><td>${formatDate(document.uploadDate)}</td></tr>
                            <tr><td><strong>File Size:</strong></td><td>${document.size}</td></tr>
                            <tr><td><strong>Category:</strong></td><td>${document.category || 'Area Document'}</td></tr>
                        </table>
                    </div>
                </div>
                ${document.description ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Description</h6>
                            <p>${document.description}</p>
                        </div>
                    </div>
                ` : ''}
                ${document.tags ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Tags</h6>
                            <div>
                                ${document.tags.split(',').map(tag => `<span class="badge bg-secondary me-1">${tag.trim()}</span>`).join('')}
                            </div>
                        </div>
                    </div>
                ` : ''}
            `;
            document.getElementById('documentDetails').innerHTML = detailsHtml;
        }

        function showRepositoryItemDetails(item) {
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Document Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Title:</strong></td><td>${item.title}</td></tr>
                            <tr><td><strong>Type:</strong></td><td><span class="badge bg-info">${item.type}</span></td></tr>
                            <tr><td><strong>Category:</strong></td><td><span class="badge bg-secondary">${item.category}</span></td></tr>
                            <tr><td><strong>Areas:</strong></td><td>${item.areas.map(area => `<span class="badge bg-primary me-1">${area}</span>`).join('')}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Upload Details</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Upload Date:</strong></td><td>${formatDate(item.uploadDate)}</td></tr>
                            <tr><td><strong>File Size:</strong></td><td>${item.size}</td></tr>
                        </table>
                    </div>
                </div>
                ${item.description ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Description</h6>
                            <p>${item.description}</p>
                        </div>
                    </div>
                ` : ''}
                ${item.tags ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Tags</h6>
                            <div>
                                ${item.tags.split(',').map(tag => `<span class="badge bg-secondary me-1">${tag.trim()}</span>`).join('')}
                            </div>
                        </div>
                    </div>
                ` : ''}
            `;
            document.getElementById('documentDetails').innerHTML = detailsHtml;
        }

        function downloadDocument() {
            // Check if we're viewing recommendations
            if (currentViewingDocumentId && currentViewingDocumentId.toString().startsWith('recommendations_')) {
                showToast('Recommendations cannot be downloaded directly.', 'info');
                return;
            }
            
            if (currentViewingDocumentId) {
                showToast('Downloading document... (simulated)', 'info');
                // In a real implementation, this would trigger file download
            }
        }

        function downloadRepositoryItem(id) {
            const item = evidenceRepository.find(item => item.id === id);
            if (item) {
                showToast(`Downloading ${item.title}... (simulated)`, 'info');
                // In a real implementation, this would trigger file download
            }
        }

        // Filter functions
        function searchRepository() {
            const searchTerm = document.getElementById('searchRepository').value.toLowerCase();
            const rows = document.querySelectorAll('#repositoryTableBody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }

        function filterRepository() { loadEvidenceRepository(); }
        function filterSurveys() { loadSelfSurveyReports(); }
        function filterResults() { loadEvaluationResults(); }

        // Action functions for deletions
        function deleteAreaDocument(id) { 
            if (confirm('Are you sure you want to delete this document?')) {
                areaDocuments = areaDocuments.filter(doc => doc.id !== id);
                loadAreaDocuments();
                showToast('Document deleted successfully!');
            }
        }

        function deleteRepositoryItem(id) {
            if (confirm('Are you sure you want to delete this item?')) {
                evidenceRepository = evidenceRepository.filter(item => item.id !== id);
                loadEvidenceRepository();
                showToast('Repository item deleted successfully!');
            }
        }

        // Survey management functions
        function viewSurvey(id) { 
            const survey = selfSurveyReports.find(s => s.id === id);
            if (!survey) return;

            const program = getEntityById(programs, survey.programId);
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Survey Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Title:</strong></td><td>${survey.title}</td></tr>
                            <tr><td><strong>Program:</strong></td><td>${program ? program.name : 'Unknown'}</td></tr>
                            <tr><td><strong>Year:</strong></td><td>${survey.year}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(survey.status)}">${survey.status}</span></td></tr>
                            <tr><td><strong>Progress:</strong></td><td>${survey.progress}%</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Areas Covered</h6>
                        <div class="mb-3">
                            ${survey.areas.map(area => `<span class="badge bg-primary me-1">${area}</span>`).join('')}
                        </div>
                        ${survey.submissionDate ? `<p><strong>Submitted:</strong> ${formatDate(survey.submissionDate)}</p>` : '<p class="text-muted">Not yet submitted</p>'}
                        <div class="progress">
                            <div class="progress-bar ${survey.progress >= 80 ? 'bg-success' : survey.progress >= 50 ? 'bg-warning' : 'bg-danger'}" style="width: ${survey.progress}%"></div>
                        </div>
                    </div>
                </div>
            `;
            
            currentViewingDocumentId = id;
            document.getElementById('documentDetails').innerHTML = detailsHtml;
            updateModalFooter('document');
            openModal('viewDocumentModal');
        }

        function editSurvey(id) { 
            const survey = selfSurveyReports.find(s => s.id === id);
            if (!survey) return;
            
            // Simulate editing by updating progress
            const newProgress = Math.min(100, survey.progress + 15);
            survey.progress = newProgress;
            
            if (newProgress === 100 && survey.status !== 'Completed') {
                survey.status = 'Completed';
                survey.submissionDate = new Date().toISOString().split('T')[0];
            }
            
            loadSelfSurveyReports();
            showToast(`Survey progress updated to ${newProgress}%!`);
        }

        function deleteSurvey(id) {
            if (confirm('Are you sure you want to delete this survey?')) {
                selfSurveyReports = selfSurveyReports.filter(survey => survey.id !== id);
                loadSelfSurveyReports();
                showToast('Survey deleted successfully!');
            }
        }

        // Evaluation results functions
        function viewEvaluationResult(id) { 
            const result = evaluationResults.find(r => r.id === id);
            if (!result) return;

            const program = getEntityById(programs, result.programId);
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Evaluation Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Program:</strong></td><td>${program ? program.name : 'Unknown'}</td></tr>
                            <tr><td><strong>Type:</strong></td><td>${result.evaluationType}</td></tr>
                            <tr><td><strong>Evaluator:</strong></td><td><span class="badge bg-info">${result.evaluator}</span></td></tr>
                            <tr><td><strong>Date:</strong></td><td>${formatDate(result.date)}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(result.status)}">${result.status}</span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Results</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Overall Rating:</strong></td><td><span class="badge ${getRatingBadgeClass(result.overallRating)}">${result.overallRating}</span></td></tr>
                            <tr><td><strong>Recommendations:</strong></td><td>${result.recommendations} item(s)</td></tr>
                        </table>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-warning" onclick="viewRecommendations(${result.id})">
                                <i class="fas fa-list me-1"></i>View Recommendations
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            currentViewingDocumentId = id;
            document.getElementById('documentDetails').innerHTML = detailsHtml;
            updateModalFooter('document');
            openModal('viewDocumentModal');
        }

        function viewRecommendations(id) { 
            const result = evaluationResults.find(r => r.id === id);
            if (!result) return;

            // Sample recommendations based on the evaluation
            const sampleRecommendations = [
                "Enhance faculty development programs in research methodology",
                "Improve laboratory equipment and facilities",
                "Strengthen industry partnerships for student internships",
                "Develop more comprehensive assessment strategies",
                "Expand library resources and digital collections"
            ];

            const recommendationsHtml = `
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary">Recommendations for ${getEntityById(programs, result.programId)?.name || 'Program'}</h6>
                        <p class="text-muted">Evaluation Date: ${formatDate(result.date)} | Evaluator: ${result.evaluator}</p>
                        <div class="list-group">
                            ${sampleRecommendations.slice(0, result.recommendations).map((rec, index) => `
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Recommendation ${index + 1}</h6>
                                        <small class="text-warning">Pending</small>
                                    </div>
                                    <p class="mb-1">${rec}</p>
                                    <small class="text-muted">Priority: ${index < 2 ? 'High' : index < 4 ? 'Medium' : 'Low'}</small>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
            
            // Set a flag to indicate we're viewing recommendations
            currentViewingDocumentId = 'recommendations_' + id;
            document.getElementById('documentDetails').innerHTML = recommendationsHtml;
            
            // Update modal footer for recommendations
            updateModalFooter('recommendations');
            openModal('viewDocumentModal');
        }

        // Helper function to update modal footer based on content type
        function updateModalFooter(contentType) {
            const modalFooter = document.querySelector('#viewDocumentModal .modal-footer');
            
            if (contentType === 'recommendations') {
                modalFooter.innerHTML = `
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                `;
            } else {
                // Default footer for documents
                modalFooter.innerHTML = `
                    <button type="button" class="btn btn-outline-primary" onclick="downloadDocument()">
                        <i class="fas fa-download me-2"></i>Download
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                `;
            }
        }

        // Helper function for updating sub-areas
        function updateSubAreas() {
            populateSubAreas();
        }
    </script>
</body>
</html> 