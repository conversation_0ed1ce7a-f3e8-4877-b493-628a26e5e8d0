<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADAMS - Accreditation Document Administration & Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Admin User</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col">
                    <h1 class="h3 text-nd-green mb-0">Dashboard</h1>
                    <p class="text-muted">Accreditation Document Administration & Management System</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stat-card bg-primary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Total Programs</h5>
                                    <h2 class="mb-0" id="totalPrograms">24</h2>
                                    <small>Active accreditation</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-graduation-cap fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stat-card bg-success text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Upcoming Visits</h5>
                                    <h2 class="mb-0" id="upcomingVisits">7</h2>
                                    <small>Next 6 months</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-check fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stat-card bg-warning text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Active Schedules</h5>
                                    <h2 class="mb-0" id="activeSchedules">5</h2>
                                    <small>In progress</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card stat-card bg-info text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Total Students</h5>
                                    <h2 class="mb-0" id="totalStudents">1,968</h2>
                                    <small>All programs</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Program Status Overview -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-nd-green text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>Program Status Overview</h5>
                        </div>
                        <div class="card-body">
                            <div class="row" id="programStatusOverview">
                                <!-- Program status cards will be populated dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Deadlines -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-nd-green text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Upcoming Deadlines</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Department</th>
                                            <th>Accreditation Type</th>
                                            <th>Deadline</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="upcomingDeadlinesTableBody">
                                        <!-- Upcoming deadlines will be populated dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- View Schedule Details Modal -->
    <div class="modal fade" id="viewScheduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Schedule Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Department:</label>
                                <p id="viewDepartment" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Program:</label>
                                <p id="viewProgram" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Agency:</label>
                                <p id="viewAgency" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Level:</label>
                                <p id="viewLevel" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Cycle:</label>
                                <p id="viewCycle" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Coverage Period:</label>
                                <p id="viewCoveragePeriod" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Visit Date:</label>
                                <p id="viewVisitDate" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status:</label>
                                <p id="viewStatus" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Priority:</label>
                                <p id="viewPriority" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Notes:</label>
                                <p id="viewNotes" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Schedule Modal -->
    <div class="modal fade" id="editScheduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editScheduleForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Department <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleDepartment" required onchange="loadEditProgramsByDepartment()">
                                        <option value="">Select Department</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Program <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleProgram" required>
                                        <option value="">Select Program</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Accrediting Agency <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleAgency" required>
                                        <option value="">Select Agency</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Level <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleLevel" required>
                                        <option value="">Select Level</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Cycle <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleCycle" required>
                                        <option value="">Select Cycle</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Coverage Period <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editScheduleCoveragePeriod" placeholder="e.g., AY 2025-2027" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Visit Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="editScheduleVisitDate" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleStatus" required>
                                        <option value="">Select Status</option>
                                        <option value="Preparation">Preparation</option>
                                        <option value="In Progress">In Progress</option>
                                        <option value="Completed">Completed</option>
                                        <option value="Postponed">Postponed</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <select class="form-select" id="editSchedulePriority">
                                        <option value="Normal">Normal</option>
                                        <option value="High">High</option>
                                        <option value="Urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="editScheduleNotes" rows="3" placeholder="Additional notes or comments"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateSchedule()">
                        <i class="fas fa-save me-2"></i>Update Schedule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Dashboard-specific JavaScript
        let currentEditingScheduleId = null;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize system data and authentication
            initializePage();
            
            // Dashboard-specific initialization
            loadUpcomingDeadlines();
            updateDashboardStats();
            loadProgramStatusOverview();
            loadRecentActivities();
        });
        
        // Load upcoming deadlines from schedules
        function loadUpcomingDeadlines() {
            const tableBody = document.getElementById('upcomingDeadlinesTableBody');
            tableBody.innerHTML = '';
            
            // Sort schedules by visit date (ascending)
            const sortedSchedules = [...schedules].sort((a, b) => new Date(a.visitDate) - new Date(b.visitDate));
            
            // Take only the first 5 upcoming schedules
            const upcomingSchedules = sortedSchedules.filter(s => new Date(s.visitDate) >= new Date()).slice(0, 5);
            
            upcomingSchedules.forEach(schedule => {
                const program = getEntityById(programs, schedule.programId);
                const department = getEntityById(departments, schedule.departmentId);
                const agency = getEntityById(agencies, schedule.agencyId);
                const level = getEntityById(levels, schedule.levelId);
                const cycle = getEntityById(cycles, schedule.cycleId);
                
                // Format the deadline date with color based on urgency
                const visitDate = new Date(schedule.visitDate);
                const today = new Date();
                const diffDays = Math.ceil((visitDate - today) / (1000 * 60 * 60 * 24));
                
                let deadlineClass = 'text-success';
                if (diffDays < 30) deadlineClass = 'text-danger';
                else if (diffDays < 90) deadlineClass = 'text-warning';
                
                const formattedDate = formatDate(schedule.visitDate);
                const statusBadge = getStatusBadge(schedule.status);
                
                const row = `
                    <tr>
                        <td><strong>${program ? program.name : 'N/A'}</strong></td>
                        <td>${department ? department.name : 'N/A'}</td>
                        <td>${agency ? agency.name : 'N/A'} ${level ? level.level : 'N/A'} ${cycle ? cycle.type : 'N/A'}</td>
                        <td><span class="${deadlineClass}">${formattedDate}</span></td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewSchedule(${schedule.id})">View</button>
                            <button class="btn btn-sm btn-outline-success" onclick="editSchedule(${schedule.id})">Update</button>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
            
            // If no upcoming schedules, show a message
            if (upcomingSchedules.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center">No upcoming deadlines found</td>
                    </tr>
                `;
            }
        }
        
        // Update dashboard statistics
        function updateDashboardStats() {
            // Calculate total programs
            const totalProgramsCount = programs.length;
            document.getElementById('totalPrograms').textContent = totalProgramsCount;
            
            // Calculate upcoming visits (next 6 months)
            const sixMonthsFromNow = new Date();
            sixMonthsFromNow.setMonth(sixMonthsFromNow.getMonth() + 6);
            const upcomingVisitsCount = schedules.filter(s => {
                const visitDate = new Date(s.visitDate);
                return visitDate >= new Date() && visitDate <= sixMonthsFromNow;
            }).length;
            document.getElementById('upcomingVisits').textContent = upcomingVisitsCount;
            
            // Calculate active schedules (In Progress and Preparation)
            const activeSchedulesCount = schedules.filter(s => 
                s.status === 'In Progress' || s.status === 'Preparation'
            ).length;
            document.getElementById('activeSchedules').textContent = activeSchedulesCount;
            
            // Calculate total students
            const totalStudentsCount = programs.reduce((total, program) => total + program.students, 0);
            document.getElementById('totalStudents').textContent = totalStudentsCount.toLocaleString();
        }
        
        // Load program status overview
        function loadProgramStatusOverview() {
            const container = document.getElementById('programStatusOverview');
            container.innerHTML = '';
            
            // Group programs by department
            const programsByDepartment = {};
            departments.forEach(dept => {
                programsByDepartment[dept.id] = {
                    department: dept,
                    programs: programs.filter(p => p.departmentId === dept.id)
                };
            });
            
            Object.values(programsByDepartment).forEach(deptData => {
                const { department, programs: deptPrograms } = deptData;
                const totalStudents = deptPrograms.reduce((sum, p) => sum + p.students, 0);
                const totalFaculty = deptPrograms.reduce((sum, p) => sum + p.faculty, 0);
                
                // Count schedules for this department
                const deptSchedules = schedules.filter(s => s.departmentId === department.id);
                const activeSchedules = deptSchedules.filter(s => s.status === 'In Progress' || s.status === 'Preparation').length;
                
                const card = `
                    <div class="col-lg-6 col-xl-4 mb-3">
                        <div class="card border-start border-4 border-primary h-100">
                            <div class="card-body">
                                <h6 class="card-title text-primary">${department.name}</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="mb-1">
                                            <strong class="d-block text-muted small">Programs</strong>
                                            <span class="fs-5 fw-bold text-dark">${deptPrograms.length}</span>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="mb-1">
                                            <strong class="d-block text-muted small">Students</strong>
                                            <span class="fs-5 fw-bold text-dark">${totalStudents}</span>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="mb-1">
                                            <strong class="d-block text-muted small">Active</strong>
                                            <span class="fs-5 fw-bold text-success">${activeSchedules}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">Dean: ${department.dean}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += card;
            });
        }
        
        // Get status badge HTML
        function getStatusBadge(status) {
            const statusMap = {
                'Preparation': 'bg-info',
                'In Progress': 'bg-warning',
                'Completed': 'bg-success',
                'Postponed': 'bg-danger',
                'Ready': 'bg-success'
            };
            return `<span class="badge ${statusMap[status] || 'bg-secondary'}">${status}</span>`;
        }
        
        // View schedule details
        function viewSchedule(id) {
            const schedule = getEntityById(schedules, id);
            if (!schedule) return;

            const program = getEntityById(programs, schedule.programId);
            const department = getEntityById(departments, schedule.departmentId);
            const agency = getEntityById(agencies, schedule.agencyId);
            const level = getEntityById(levels, schedule.levelId);
            const cycle = getEntityById(cycles, schedule.cycleId);

            document.getElementById('viewDepartment').textContent = department ? department.name : 'N/A';
            document.getElementById('viewProgram').textContent = program ? program.name : 'N/A';
            document.getElementById('viewAgency').textContent = agency ? agency.name : 'N/A';
            document.getElementById('viewLevel').textContent = level ? level.level : 'N/A';
            document.getElementById('viewCycle').textContent = cycle ? cycle.type : 'N/A';
            document.getElementById('viewCoveragePeriod').textContent = schedule.coveragePeriod;
            document.getElementById('viewVisitDate').textContent = formatDate(schedule.visitDate);
            document.getElementById('viewStatus').innerHTML = getStatusBadge(schedule.status);
            document.getElementById('viewPriority').textContent = schedule.priority || 'Normal';
            document.getElementById('viewNotes').textContent = schedule.notes || 'No notes';

            openModal('viewScheduleModal');
        }
        
        // Load programs by department for edit modal
        function loadEditProgramsByDepartment() {
            const departmentId = document.getElementById('editScheduleDepartment').value;
            const programSelect = document.getElementById('editScheduleProgram');
            
            programSelect.innerHTML = '<option value="">Select Program</option>';
            
            if (departmentId) {
                const departmentPrograms = programs.filter(p => p.departmentId == departmentId);
                departmentPrograms.forEach(program => {
                    programSelect.innerHTML += `<option value="${program.id}">${program.name}</option>`;
                });
            }
        }

        // Edit schedule
        function editSchedule(id) {
            const schedule = getEntityById(schedules, id);
            if (!schedule) return;

            currentEditingScheduleId = id;

            // Populate dropdowns
            populateDropdowns();

            // Populate edit form
            document.getElementById('editScheduleDepartment').value = schedule.departmentId;
            loadEditProgramsByDepartment();
            setTimeout(() => {
                document.getElementById('editScheduleProgram').value = schedule.programId;
            }, 100);
            
            document.getElementById('editScheduleAgency').value = schedule.agencyId;
            document.getElementById('editScheduleLevel').value = schedule.levelId;
            document.getElementById('editScheduleCycle').value = schedule.cycleId;
            document.getElementById('editScheduleCoveragePeriod').value = schedule.coveragePeriod;
            document.getElementById('editScheduleVisitDate').value = schedule.visitDate;
            document.getElementById('editScheduleStatus').value = schedule.status;
            document.getElementById('editSchedulePriority').value = schedule.priority || 'Normal';
            document.getElementById('editScheduleNotes').value = schedule.notes || '';

            openModal('editScheduleModal');
        }

        // Populate dropdowns
        function populateDropdowns() {
            // Populate departments
            const departmentSelect = document.getElementById('editScheduleDepartment');
            departmentSelect.innerHTML = '<option value="">Select Department</option>';
            departments.forEach(dept => {
                departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
            });

            // Populate agencies
            const agencySelect = document.getElementById('editScheduleAgency');
            agencySelect.innerHTML = '<option value="">Select Agency</option>';
            agencies.forEach(agency => {
                agencySelect.innerHTML += `<option value="${agency.id}">${agency.name}</option>`;
            });

            // Populate levels
            const levelSelect = document.getElementById('editScheduleLevel');
            levelSelect.innerHTML = '<option value="">Select Level</option>';
            levels.forEach(level => {
                levelSelect.innerHTML += `<option value="${level.id}">${level.level}</option>`;
            });

            // Populate cycles
            const cycleSelect = document.getElementById('editScheduleCycle');
            cycleSelect.innerHTML = '<option value="">Select Cycle</option>';
            cycles.forEach(cycle => {
                cycleSelect.innerHTML += `<option value="${cycle.id}">${cycle.type}</option>`;
            });
        }

        // Logout function
        function logoutUser() {
            if (confirm('Are you sure you want to logout?')) {
                sessionStorage.removeItem('currentUser');
                showToast('Logged out successfully!', 'success');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
            }
        }

        // Show toast notification
        function showToast(message, type = 'success') {
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '1056';
                document.body.appendChild(toastContainer);
            }

            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'error' ? 'danger' : type;
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-bg-${bgClass}" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (toastElement) {
                    toastElement.remove();
                }
            }, 5000);
        }


        
        // Get appropriate icon for activity type
        function getActivityIcon(action) {
            switch(action) {
                case 'Schedule Created': return 'fas fa-plus';
                case 'Schedule Updated': return 'fas fa-edit';
                case 'Schedule Deleted': return 'fas fa-trash';
                case 'Document Uploaded': return 'fas fa-upload';
                case 'Document Approved': return 'fas fa-check';
                case 'Document Rejected': return 'fas fa-times';
                case 'Document Review Started': return 'fas fa-eye';
                default: return 'fas fa-info';
            }
        }
        
        // Get appropriate background class for activity type
        function getActivityBgClass(action) {
            switch(action) {
                case 'Schedule Created': return 'bg-primary';
                case 'Schedule Updated': return 'bg-warning';
                case 'Schedule Deleted': return 'bg-danger';
                case 'Document Uploaded': return 'bg-info';
                case 'Document Approved': return 'bg-success';
                case 'Document Rejected': return 'bg-danger';
                case 'Document Review Started': return 'bg-warning';
                default: return 'bg-secondary';
            }
        }
        
        // Get time ago string
        function getTimeAgo(timestamp) {
            const now = new Date();
            const activityTime = new Date(timestamp);
            const diffMs = now - activityTime;
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            
            if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
            if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
            if (diffMinutes > 0) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
            return 'Just now';
        }
        
        // Update schedule using new tracking system
        function updateSchedule() {
            if (!validateForm('editScheduleForm')) return;

            const updateData = {
                programId: parseInt(document.getElementById('editScheduleProgram').value),
                departmentId: parseInt(document.getElementById('editScheduleDepartment').value),
                agencyId: parseInt(document.getElementById('editScheduleAgency').value),
                levelId: parseInt(document.getElementById('editScheduleLevel').value),
                cycleId: parseInt(document.getElementById('editScheduleCycle').value),
                coveragePeriod: document.getElementById('editScheduleCoveragePeriod').value,
                visitDate: document.getElementById('editScheduleVisitDate').value,
                status: document.getElementById('editScheduleStatus').value,
                priority: document.getElementById('editSchedulePriority').value || 'Normal',
                notes: document.getElementById('editScheduleNotes').value || ''
            };

            const result = updateScheduleWithTracking(currentEditingScheduleId, updateData);
            if (result) {
                loadUpcomingDeadlines();
                updateDashboardStats();

                closeModal('editScheduleModal');
                showToast('Schedule updated successfully!');
            }
        }
    </script>
</body>
</html> 