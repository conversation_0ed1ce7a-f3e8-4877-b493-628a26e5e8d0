<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accreditation Levels - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="true">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse show" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link active" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-chart-line me-3"></i>Accreditation Levels</h1>
                    <p>Manage accreditation levels and their requirements for institutional quality assurance</p>
                </div>
            </div>

            <!-- Search and Filter Bar -->
            <div class="search-filter-bar">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="searchLevels" placeholder="Search levels..." onkeyup="searchTable(this.value, 'levelsTableBody')">
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-nd-green" onclick="openAddLevelModal()">
                            <i class="fas fa-plus me-2"></i>Add Level
                        </button>
                        <button class="btn btn-outline-secondary" onclick="exportLevels()">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                    </div>
                </div>
            </div>

            <!-- Levels Table -->
            <div class="data-table-container">
                <div class="data-table-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</h5>
                </div>
                <div class="data-table-body">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Level</th>
                                    <th>Description</th>
                                    <th>Requirements</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="levelsTableBody">
                                <!-- Level rows will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Level Modal -->
    <div class="modal fade" id="addLevelModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Level</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addLevelForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Level Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="levelName" placeholder="e.g., Level I" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="levelStatus" required>
                                        <option value="">Select Status</option>
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Description <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="levelDescription" placeholder="e.g., Preliminary Candidacy Status" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Requirements <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="levelRequirements" rows="4" placeholder="Describe the requirements for this level..." required></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveLevel()">
                        <i class="fas fa-save me-2"></i>Save Level
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Level Modal -->
    <div class="modal fade" id="editLevelModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Level</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editLevelForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Level Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editLevelName" placeholder="e.g., Level I" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editLevelStatus" required>
                                        <option value="">Select Status</option>
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Description <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editLevelDescription" placeholder="e.g., Preliminary Candidacy Status" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Requirements <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="editLevelRequirements" rows="4" placeholder="Describe the requirements for this level..." required></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateLevel()">
                        <i class="fas fa-save me-2"></i>Update Level
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Level Details Modal -->
    <div class="modal fade" id="viewLevelModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Level Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Level:</label>
                                <p id="viewLevelName" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <p id="viewLevelStatus" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Description:</label>
                                <p id="viewLevelDescription" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Requirements:</label>
                                <p id="viewLevelRequirements" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Page-specific JavaScript for Levels
        let currentEditingLevelId = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadLevels();
        });

        // Load and display levels
        function loadLevels() {
            const tableBody = document.getElementById('levelsTableBody');
            tableBody.innerHTML = '';

            levels.forEach(level => {
                const statusBadge = level.status ? 
                    `<span class="badge ${level.status === 'Active' ? 'bg-success' : 'bg-danger'}">${level.status}</span>` :
                    `<span class="badge bg-success">Active</span>`;

                const row = `
                    <tr>
                        <td>${level.id}</td>
                        <td><strong>${level.level}</strong></td>
                        <td>${level.description}</td>
                        <td>${level.requirements}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-info" onclick="viewLevel(${level.id})" title="View Details">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="editLevel(${level.id})" title="Edit">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteLevel(${level.id})" title="Delete">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.innerHTML += row;
            });
        }

        // Open add level modal
        function openAddLevelModal() {
            resetForm('addLevelForm');
            openModal('addLevelModal');
        }

        // Save new level
        function saveLevel() {
            if (!validateForm('addLevelForm')) return;

            const newLevel = {
                id: generateId(levels),
                level: document.getElementById('levelName').value,
                description: document.getElementById('levelDescription').value,
                requirements: document.getElementById('levelRequirements').value,
                status: document.getElementById('levelStatus').value
            };

            levels.push(newLevel);
            loadLevels();
            closeModal('addLevelModal');
            showToast('Level added successfully!');
        }

        // View level details
        function viewLevel(id) {
            const level = getEntityById(levels, id);
            if (!level) return;

            document.getElementById('viewLevelName').textContent = level.level;
            document.getElementById('viewLevelStatus').innerHTML = level.status ? 
                `<span class="badge ${level.status === 'Active' ? 'bg-success' : 'bg-danger'}">${level.status}</span>` :
                `<span class="badge bg-success">Active</span>`;
            document.getElementById('viewLevelDescription').textContent = level.description;
            document.getElementById('viewLevelRequirements').textContent = level.requirements;

            openModal('viewLevelModal');
        }

        // Edit level
        function editLevel(id) {
            const level = getEntityById(levels, id);
            if (!level) return;

            currentEditingLevelId = id;

            document.getElementById('editLevelName').value = level.level;
            document.getElementById('editLevelDescription').value = level.description;
            document.getElementById('editLevelRequirements').value = level.requirements;
            document.getElementById('editLevelStatus').value = level.status || 'Active';

            openModal('editLevelModal');
        }

        // Update level
        function updateLevel() {
            if (!validateForm('editLevelForm')) return;

            const levelIndex = levels.findIndex(l => l.id === currentEditingLevelId);
            if (levelIndex === -1) return;

            levels[levelIndex] = {
                ...levels[levelIndex],
                level: document.getElementById('editLevelName').value,
                description: document.getElementById('editLevelDescription').value,
                requirements: document.getElementById('editLevelRequirements').value,
                status: document.getElementById('editLevelStatus').value
            };

            loadLevels();
            closeModal('editLevelModal');
            showToast('Level updated successfully!');
        }

        // Delete level
        function deleteLevel(id) {
            const level = getEntityById(levels, id);
            if (!level) return;

            if (confirm(`Are you sure you want to delete "${level.level}"?`)) {
                levels = levels.filter(l => l.id !== id);
                loadLevels();
                showToast('Level deleted successfully!');
            }
        }

        // Export levels to CSV
        function exportLevels() {
            const csvData = levels.map(level => ({
                'ID': level.id,
                'Level': level.level,
                'Description': level.description,
                'Requirements': level.requirements,
                'Status': level.status || 'Active'
            }));

            exportToCSV(csvData, 'accreditation_levels');
        }
    </script>
</body>
</html> 