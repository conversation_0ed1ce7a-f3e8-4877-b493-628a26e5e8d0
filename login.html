<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADAMS - Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
        }
        
        .login-left {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .role-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .role-card:hover {
            border-color: #27ae60;
            background-color: #f8f9fa;
        }
        
        .role-card.selected {
            border-color: #27ae60;
            background-color: #e8f5e8;
        }
        
        .demo-credentials {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9em;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Left Panel -->
                <div class="col-lg-6 login-left">
                    <div>
                        <h1 class="display-4 mb-4">
                            <i class="fas fa-graduation-cap me-3"></i>ADAMS
                        </h1>
                        <h3 class="mb-4">Accreditation Document & Assessment Management System</h3>
                        <p class="lead mb-4">
                            Streamline your institution's accreditation process with comprehensive document management, 
                            quality standards tracking, and cycle monitoring.
                        </p>
                        <div class="row text-center mt-5">
                            <div class="col-4">
                                <i class="fas fa-file-alt fa-2x mb-2"></i>
                                <p>Document<br>Management</p>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <p>Quality<br>Standards</p>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-sync fa-2x mb-2"></i>
                                <p>Cycle<br>Tracking</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Panel -->
                <div class="col-lg-6 login-right">
                    <div class="text-center mb-4">
                        <h2>Welcome Back</h2>
                        <p class="text-muted">Please sign in to your account</p>
                    </div>
                    
                    <!-- Login Form -->
                    <form id="loginForm">
                        <div class="mb-3">
                            <label class="form-label">Select Your Role</label>
                            <div id="roleSelection">
                                <div class="role-card" data-role="superadmin">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-crown text-warning me-3 fa-lg"></i>
                                        <div>
                                            <strong>Super Admin</strong>
                                            <div class="text-muted small">Full system control and administration</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="role-card" data-role="admin">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-shield text-primary me-3 fa-lg"></i>
                                        <div>
                                            <strong>Admin</strong>
                                            <div class="text-muted small">Full access to all modules, user controls, and reports</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="role-card" data-role="areachair">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-tie text-success me-3 fa-lg"></i>
                                        <div>
                                            <strong>Area Chair</strong>
                                            <div class="text-muted small">Access to assigned area, can review/approve documents</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="role-card" data-role="subareachair">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-edit text-info me-3 fa-lg"></i>
                                        <div>
                                            <strong>Sub-Area Chair</strong>
                                            <div class="text-muted small">Can upload, organize, and tag documents within assigned sub-area</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="role-card" data-role="member">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user text-secondary me-3 fa-lg"></i>
                                        <div>
                                            <strong>Member</strong>
                                            <div class="text-muted small">Upload/view their own documents only; no review permissions</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                        
                        <button type="submit" class="btn btn-login w-100 mb-3">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </button>
                    </form>
                    
                    <!-- Demo Credentials -->
                    <div class="demo-credentials">
                        <h6><i class="fas fa-info-circle me-2"></i>Demo Credentials</h6>
                        <div class="row">
                            <div class="col-6">
                                <small><strong>Super Admin:</strong><br>admin1 / hazel</small><br>
                                <small><strong>Admin:</strong><br>admin / admin</small><br>
                                <small><strong>Area Chair:</strong><br>chair1 / admin</small>
                            </div>
                            <div class="col-6">
                                <small><strong>Sub-Area Chair:</strong><br>subchair1 / admin</small><br>
                                <small><strong>Member:</strong><br>member1 / admin</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize system data when login page loads
        function initializeSystemData() {
            if (!localStorage.getItem('adamsSystemData')) {
                const defaultData = {
                    programs: [],
                    documents: [],
                    cycles: [],
                    notifications: [],
                    reports: [],
                    standards: []
                };
                localStorage.setItem('adamsSystemData', JSON.stringify(defaultData));
            }
            
            if (!localStorage.getItem('adamsSystemUsers')) {
                const defaultUsers = {
                    'admin1': { password: 'hazel', role: 'superadmin', name: 'Super Administrator', areas: 'all', status: 'active', email: '<EMAIL>', lastLogin: null },
                    'admin': { password: 'admin', role: 'admin', name: 'System Administrator', areas: 'all', status: 'active', email: '<EMAIL>', lastLogin: null },
                    'chair1': { password: 'admin', role: 'areachair', name: 'Area Chair 1', areas: [1, 2], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'chair2': { password: 'admin', role: 'areachair', name: 'Area Chair 2', areas: [3, 4], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'chair3': { password: 'admin', role: 'areachair', name: 'Area Chair 3', areas: [5, 6], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'chair4': { password: 'admin', role: 'areachair', name: 'Area Chair 4', areas: [7, 8], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'subchair1': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 1', areas: [1], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'subchair2': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 2', areas: [2], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'subchair3': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 3', areas: [3], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'member1': { password: 'admin', role: 'member', name: 'Faculty Member 1', areas: [], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'member2': { password: 'admin', role: 'member', name: 'Faculty Member 2', areas: [], status: 'active', email: '<EMAIL>', lastLogin: null },
                    'member3': { password: 'admin', role: 'member', name: 'Faculty Member 3', areas: [], status: 'active', email: '<EMAIL>', lastLogin: null }
                };
                localStorage.setItem('adamsSystemUsers', JSON.stringify(defaultUsers));
            }
        }

        function getDashboardForRole(role) {
            switch(role) {
                case 'superadmin':
                case 'admin':
                    return 'index.html';
                case 'areachair':
                    return 'dashboard-areachair.html';
                case 'subareachair':
                    return 'dashboard-subareachair.html';
                case 'member':
                    return 'dashboard-member.html';
                default:
                    return 'index.html';
            }
        }

        function updateUserLastLogin(username) {
            const users = JSON.parse(localStorage.getItem('adamsSystemUsers') || '{}');
            if (users[username]) {
                users[username].lastLogin = new Date().toISOString();
                localStorage.setItem('adamsSystemUsers', JSON.stringify(users));
            }
        }

        function showToast(message, type = 'info') {
            // Remove existing toasts
            const existingToasts = document.querySelectorAll('.toast-notification');
            existingToasts.forEach(toast => toast.remove());

            // Create toast element
            const toast = document.createElement('div');
            toast.className = `toast-notification alert alert-${type} alert-dismissible fade show`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            `;
            
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(toast);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }

        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showToast('Please select a role and enter your password', 'warning');
                return;
            }

            const users = JSON.parse(localStorage.getItem('adamsSystemUsers') || '{}');
            const user = users[username];
            
            if (!user) {
                showToast('User not found', 'error');
                return;
            }

            if (user.password !== password) {
                showToast('Invalid password', 'error');
                return;
            }

            if (user.status !== 'active') {
                showToast('Account is inactive. Please contact administrator', 'error');
                return;
            }

            // Update last login
            updateUserLastLogin(username);
            
            // Set session
            const userData = {
                username: username,
                name: user.name,
                role: user.role,
                email: user.email,
                areas: user.areas,
                status: user.status
            };
            
            sessionStorage.setItem('currentUser', JSON.stringify(userData));
            
            showToast(`Welcome, ${user.name}!`, 'success');
            
            // Redirect to appropriate dashboard
            setTimeout(() => {
                window.location.href = getDashboardForRole(user.role);
            }, 1000);
        }

        // Initialize data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystemData();
            
            // Check if user is already logged in
            const currentUser = sessionStorage.getItem('currentUser');
            if (currentUser) {
                const userData = JSON.parse(currentUser);
                window.location.href = getDashboardForRole(userData.role);
            }
            
            // Set up role selection
            const roleCards = document.querySelectorAll('.role-card');
            let selectedRole = null;
            
            roleCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Remove selected class from all cards
                    roleCards.forEach(c => c.classList.remove('selected'));
                    
                    // Add selected class to clicked card
                    this.classList.add('selected');
                    selectedRole = this.dataset.role;
                    
                    // Auto-fill credentials based on role
                    const usernameInput = document.getElementById('username');
                    const passwordInput = document.getElementById('password');
                    
                    switch(selectedRole) {
                        case 'superadmin':
                            usernameInput.value = 'admin1';
                            passwordInput.placeholder = 'Enter password (hint: hazel)';
                            break;
                        case 'admin':
                            usernameInput.value = 'admin';
                            passwordInput.placeholder = 'Enter password (hint: admin)';
                            break;
                        case 'areachair':
                            usernameInput.value = 'chair1';
                            passwordInput.placeholder = 'Enter password (hint: admin)';
                            break;
                        case 'subareachair':
                            usernameInput.value = 'subchair1';
                            passwordInput.placeholder = 'Enter password (hint: admin)';
                            break;
                        case 'member':
                            usernameInput.value = 'member1';
                            passwordInput.placeholder = 'Enter password (hint: admin)';
                            break;
                    }
                    
                    // Focus on password field
                    passwordInput.focus();
                });
            });
            
            // Set up form submission
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });
            
            // Set up enter key for password field
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });
    </script>
</body>
</html> 