<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-bell me-3"></i>Notifications</h1>
                        <p>View system alerts, reminders, and announcements</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary me-2" onclick="markAllAsRead()">
                            <i class="fas fa-check-double me-2"></i>Mark All as Read
                        </button>
                        <button class="btn btn-nd-green" onclick="openCreateNotificationModal()">
                            <i class="fas fa-plus me-2"></i>Create Notification
                        </button>
                    </div>
                </div>
            </div>

            <!-- Notification Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <select class="form-select" id="notificationFilter" onchange="filterNotifications()">
                                        <option value="all">All Notifications</option>
                                        <option value="unread">Unread Only</option>
                                        <option value="alerts">System Alerts</option>
                                        <option value="reminders">Reminders</option>
                                        <option value="announcements">Announcements</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="priorityFilter" onchange="filterNotifications()">
                                        <option value="">All Priorities</option>
                                        <option value="urgent">Urgent</option>
                                        <option value="high">High</option>
                                        <option value="medium">Medium</option>
                                        <option value="low">Low</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchNotifications" placeholder="Search notifications..." onkeyup="searchNotifications()">
                                </div>
                                <div class="col-md-3">
                                    <div class="text-end">
                                        <span class="badge bg-danger" id="unreadCount">5</span> Unread
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-nd-green text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i>Notification Center</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="notification-list" id="notificationsList">
                                <!-- System Alert -->
                                <div class="notification-item unread" data-type="alerts" data-priority="urgent" data-id="1">
                                    <div class="notification-icon bg-danger">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <h6 class="notification-title">System Alert: Backup Failed</h6>
                                            <span class="notification-time text-muted">2 hours ago</span>
                                            <span class="badge bg-danger ms-2">Urgent</span>
                                        </div>
                                        <p class="notification-message">Automated system backup failed at 2:00 AM. Please check server status and retry backup process.</p>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">Mark as Read</button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="viewDetails('1')">View Details</button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(1)">Delete</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Deadline Reminder -->
                                <div class="notification-item unread" data-type="reminders" data-priority="high" data-id="2">
                                    <div class="notification-icon bg-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <h6 class="notification-title">Deadline Reminder: BSIT Accreditation Visit</h6>
                                            <span class="notification-time text-muted">4 hours ago</span>
                                            <span class="badge bg-warning ms-2">High</span>
                                        </div>
                                        <p class="notification-message">BSIT program accreditation visit is scheduled for January 15, 2025. Ensure all documents are prepared and submitted.</p>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">Mark as Read</button>
                                            <button class="btn btn-sm btn-outline-success" onclick="viewSchedule('schedule1')">View Schedule</button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(2)">Delete</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Document Upload -->
                                <div class="notification-item unread" data-type="alerts" data-priority="medium" data-id="3">
                                    <div class="notification-icon bg-info">
                                        <i class="fas fa-upload"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <h6 class="notification-title">Document Upload Notification</h6>
                                            <span class="notification-time text-muted">6 hours ago</span>
                                            <span class="badge bg-info ms-2">Medium</span>
                                        </div>
                                        <p class="notification-message">Dr. Maria Santos uploaded "Faculty Development Plan 2025" for BSCS program review.</p>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">Mark as Read</button>
                                            <button class="btn btn-sm btn-outline-info" onclick="viewDocument('doc1')">View Document</button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(3)">Delete</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- System Announcement -->
                                <div class="notification-item unread" data-type="announcements" data-priority="medium" data-id="4">
                                    <div class="notification-icon bg-success">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <h6 class="notification-title">System Maintenance Announcement</h6>
                                            <span class="notification-time text-muted">1 day ago</span>
                                            <span class="badge bg-success ms-2">Medium</span>
                                        </div>
                                        <p class="notification-message">Scheduled system maintenance on January 20, 2025 from 12:00 AM to 6:00 AM. The system will be temporarily unavailable.</p>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">Mark as Read</button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="viewDetails('4')">Read More</button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(4)">Delete</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- User Assignment -->
                                <div class="notification-item unread" data-type="alerts" data-priority="low" data-id="5">
                                    <div class="notification-icon bg-primary">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <h6 class="notification-title">New User Assignment</h6>
                                            <span class="notification-time text-muted">2 days ago</span>
                                            <span class="badge bg-secondary ms-2">Low</span>
                                        </div>
                                        <p class="notification-message">Dr. John Dela Cruz has been assigned as Area Chair for CITE department accreditation cycle.</p>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">Mark as Read</button>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewUser('user1')">View User</button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(5)">Delete</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Read Notifications -->
                                <div class="notification-item read" data-type="reminders" data-priority="medium" data-id="6">
                                    <div class="notification-icon bg-secondary">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <h6 class="notification-title">Document Review Completed</h6>
                                            <span class="notification-time text-muted">3 days ago</span>
                                            <span class="badge bg-secondary ms-2">Medium</span>
                                        </div>
                                        <p class="notification-message">All required documents for BSBA program have been reviewed and approved.</p>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-success" onclick="viewDetails('6')">View Details</button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(6)">Delete</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Create Notification Modal -->
    <div class="modal fade" id="createNotificationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Create New Notification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createNotificationForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Notification Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="notificationType" required>
                                        <option value="">Select Type</option>
                                        <option value="alert">System Alert</option>
                                        <option value="reminder">Reminder</option>
                                        <option value="announcement">Announcement</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority <span class="text-danger">*</span></label>
                                    <select class="form-select" id="notificationPriority" required>
                                        <option value="">Select Priority</option>
                                        <option value="low">Low</option>
                                        <option value="medium">Medium</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="notificationTitle" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="notificationMessage" rows="4" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Recipients</label>
                                    <select class="form-select" id="notificationRecipients">
                                        <option value="all">All Users</option>
                                        <option value="admins">Administrators Only</option>
                                        <option value="department">Department Heads</option>
                                        <option value="area-chairs">Area Chairs</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Schedule Send</label>
                                    <input type="datetime-local" class="form-control" id="scheduleDateTime">
                                    <small class="text-muted">Leave blank to send immediately</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="createNotification()">
                        <i class="fas fa-paper-plane me-2"></i>Send Notification
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Notification data array to store notifications dynamically
        let notifications = [];
        let notificationIdCounter = 1000;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeNotifications();
            updateUnreadCount();
        });

        // Initialize existing notifications
        function initializeNotifications() {
            const existingNotifications = document.querySelectorAll('.notification-item');
            existingNotifications.forEach((item, index) => {
                const notification = {
                    id: index + 1,
                    type: item.dataset.type,
                    priority: item.dataset.priority,
                    title: item.querySelector('.notification-title').textContent,
                    message: item.querySelector('.notification-message').textContent,
                    time: item.querySelector('.notification-time').textContent,
                    read: item.classList.contains('read'),
                    createdAt: new Date()
                };
                notifications.push(notification);
            });
        }

        // Mark individual notification as read
        function markAsRead(button) {
            const notificationItem = button.closest('.notification-item');
            const notificationId = parseInt(notificationItem.dataset.id);
            
            notificationItem.classList.remove('unread');
            notificationItem.classList.add('read');
            
            // Update in data array
            const notification = notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.read = true;
            }
            
            // Update the actions
            const actions = notificationItem.querySelector('.notification-actions');
            button.remove();
            
            updateUnreadCount();
            showToast('Notification marked as read');
        }

        // Mark all notifications as read
        function markAllAsRead() {
            const unreadNotifications = document.querySelectorAll('.notification-item.unread');
            unreadNotifications.forEach(notification => {
                const notificationId = parseInt(notification.dataset.id);
                
                notification.classList.remove('unread');
                notification.classList.add('read');
                
                // Update in data array
                const notificationData = notifications.find(n => n.id === notificationId);
                if (notificationData) {
                    notificationData.read = true;
                }
                
                // Remove mark as read button
                const markReadBtn = notification.querySelector('.notification-actions button[onclick*="markAsRead"]');
                if (markReadBtn) {
                    markReadBtn.remove();
                }
            });
            
            updateUnreadCount();
            showToast('All notifications marked as read');
        }

        // Update unread count
        function updateUnreadCount() {
            const unreadCount = document.querySelectorAll('.notification-item.unread').length;
            document.getElementById('unreadCount').textContent = unreadCount;
        }

        // Filter notifications
        function filterNotifications() {
            const typeFilter = document.getElementById('notificationFilter').value;
            const priorityFilter = document.getElementById('priorityFilter').value;
            const notifications = document.querySelectorAll('.notification-item');

            notifications.forEach(notification => {
                let showNotification = true;

                // Filter by type
                if (typeFilter !== 'all') {
                    if (typeFilter === 'unread' && !notification.classList.contains('unread')) {
                        showNotification = false;
                    } else if (typeFilter !== 'unread' && notification.dataset.type !== typeFilter) {
                        showNotification = false;
                    }
                }

                // Filter by priority
                if (priorityFilter && notification.dataset.priority !== priorityFilter) {
                    showNotification = false;
                }

                notification.style.display = showNotification ? 'flex' : 'none';
            });
        }

        // Search notifications
        function searchNotifications() {
            const searchTerm = document.getElementById('searchNotifications').value.toLowerCase();
            const notifications = document.querySelectorAll('.notification-item');

            notifications.forEach(notification => {
                const title = notification.querySelector('.notification-title').textContent.toLowerCase();
                const message = notification.querySelector('.notification-message').textContent.toLowerCase();
                
                const matches = title.includes(searchTerm) || message.includes(searchTerm);
                notification.style.display = matches ? 'flex' : 'none';
            });
        }

        // Open create notification modal
        function openCreateNotificationModal() {
            resetForm('createNotificationForm');
            openModal('createNotificationModal');
        }

        // Create new notification
        function createNotification() {
            if (!validateForm('createNotificationForm')) return;

            const type = document.getElementById('notificationType').value;
            const priority = document.getElementById('notificationPriority').value;
            const title = document.getElementById('notificationTitle').value;
            const message = document.getElementById('notificationMessage').value;
            const recipients = document.getElementById('notificationRecipients').value;
            const scheduleDateTime = document.getElementById('scheduleDateTime').value;

            // Create notification object
            const newNotification = {
                id: ++notificationIdCounter,
                type: type,
                priority: priority,
                title: title,
                message: message,
                recipients: recipients,
                scheduleDateTime: scheduleDateTime,
                time: scheduleDateTime ? `Scheduled for ${new Date(scheduleDateTime).toLocaleString()}` : 'Just now',
                read: false,
                createdAt: new Date()
            };

            // Add to notifications array
            notifications.unshift(newNotification);

            // Add to DOM
            addNotificationToDOM(newNotification);

            // Update unread count
            updateUnreadCount();

            // Close modal and show success
            closeModal('createNotificationModal');
            showToast(`Notification created successfully! ${scheduleDateTime ? 'Scheduled for ' + new Date(scheduleDateTime).toLocaleString() : 'Sent immediately'}`);
        }

        // Add notification to DOM
        function addNotificationToDOM(notification) {
            const notificationsList = document.getElementById('notificationsList');
            
            // Get appropriate icon and color based on type
            const iconData = getNotificationIconData(notification.type);
            
            // Create notification HTML
            const notificationHTML = `
                <div class="notification-item unread" data-type="${notification.type}" data-priority="${notification.priority}" data-id="${notification.id}">
                    <div class="notification-icon ${iconData.bgClass}">
                        <i class="${iconData.icon}"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-header">
                            <h6 class="notification-title">${notification.title}</h6>
                            <span class="notification-time text-muted">${notification.time}</span>
                            <span class="badge ${getPriorityBadgeClass(notification.priority)} ms-2">${notification.priority}</span>
                        </div>
                        <p class="notification-message">${notification.message}</p>
                        <div class="notification-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(this)">Mark as Read</button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="viewDetails('${notification.id}')">View Details</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(${notification.id})">Delete</button>
                        </div>
                    </div>
                </div>
            `;
            
            // Insert at the beginning of the list
            notificationsList.insertAdjacentHTML('afterbegin', notificationHTML);
        }

        // Delete notification
        function deleteNotification(id) {
            const confirmed = confirm('Are you sure you want to delete this notification?');
            if (confirmed) {
                // Remove from DOM
                const notificationElement = document.querySelector(`[data-id="${id}"]`);
                if (notificationElement) {
                    notificationElement.remove();
                }
                
                // Remove from data array
                notifications = notifications.filter(n => n.id !== id);
                
                // Update unread count
                updateUnreadCount();
                
                showToast('Notification deleted successfully');
            }
        }

        // Get notification icon data based on type
        function getNotificationIconData(type) {
            switch(type) {
                case 'alert':
                    return { icon: 'fas fa-exclamation-triangle', bgClass: 'bg-danger' };
                case 'reminder':
                    return { icon: 'fas fa-clock', bgClass: 'bg-warning' };
                case 'announcement':
                    return { icon: 'fas fa-bullhorn', bgClass: 'bg-success' };
                default:
                    return { icon: 'fas fa-bell', bgClass: 'bg-info' };
            }
        }

        // Get priority badge class
        function getPriorityBadgeClass(priority) {
            switch(priority) {
                case 'urgent': return 'bg-danger';
                case 'high': return 'bg-warning';
                case 'medium': return 'bg-info';
                case 'low': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        // Enhanced notification actions
        function viewDetails(id) {
            const notification = notifications.find(n => n.id == id);
            if (notification) {
                const details = `Notification Details:
                
Title: ${notification.title}
Type: ${notification.type}
Priority: ${notification.priority}
Created: ${notification.createdAt.toLocaleString()}
Recipients: ${notification.recipients}
${notification.scheduleDateTime ? `Scheduled: ${new Date(notification.scheduleDateTime).toLocaleString()}` : ''}

Message:
${notification.message}`;
                
                alert(details);
            } else {
                showToast(`Viewing details for notification ${id}`);
            }
        }

        // Clear all notifications
        function clearAllNotifications() {
            const confirmed = confirm('Are you sure you want to clear all notifications? This action cannot be undone.');
            if (confirmed) {
                document.getElementById('notificationsList').innerHTML = '<div class="text-center py-4 text-muted"><i class="fas fa-bell-slash fa-3x mb-3"></i><p>No notifications</p></div>';
                notifications = [];
                updateUnreadCount();
                showToast('All notifications cleared');
            }
        }

        // Export notifications to CSV
        function exportNotifications() {
            const csvData = `Notification Export Report
Generated Date,${new Date().toLocaleString()}

ID,Title,Type,Priority,Message,Created,Status,Recipients
${notifications.map(n => 
    `${n.id},"${n.title}",${n.type},${n.priority},"${n.message}",${n.createdAt.toLocaleString()},${n.read ? 'Read' : 'Unread'},${n.recipients}`
).join('\n')}`;
            
            downloadCSV(csvData, 'Notifications_Export.csv');
            showToast('Notifications exported successfully!');
        }

        // Helper function to download CSV
        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // Add clear all and export buttons to the page header
        document.addEventListener('DOMContentLoaded', function() {
            const headerButtons = document.querySelector('.page-header .container-fluid > div:last-child');
            if (headerButtons) {
                headerButtons.innerHTML += `
                    <button class="btn btn-outline-warning me-2" onclick="clearAllNotifications()">
                        <i class="fas fa-trash me-2"></i>Clear All
                    </button>
                    <button class="btn btn-outline-info me-2" onclick="exportNotifications()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                `;
            }
        });

        // Navigation functions for notification actions
        function viewSchedule(id) {
            window.location.href = 'schedules.html';
        }

        function viewDocument(id) {
            window.location.href = 'documents.html';
        }

        function viewUser(id) {
            window.location.href = 'users.html';
        }
    </script>

    <style>
        .notification-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .notification-item {
            display: flex;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
        }

        .notification-item.unread {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .notification-item.read {
            opacity: 0.7;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .notification-content {
            flex: 1;
        }

        .notification-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .notification-title {
            margin: 0;
            margin-right: auto;
            font-weight: 600;
        }

        .notification-time {
            font-size: 0.875rem;
        }

        .notification-message {
            margin-bottom: 1rem;
            color: #6c757d;
        }

        .notification-actions {
            display: flex;
            gap: 0.5rem;
        }
    </style>
</body>
</html> 