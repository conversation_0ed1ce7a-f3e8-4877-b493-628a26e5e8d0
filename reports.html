<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-chart-bar me-3"></i>Reports</h1>
                    <p>Generate and export accreditation reports and summaries</p>
                </div>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="accreditation-summary-tab" data-bs-toggle="tab" data-bs-target="#accreditation-summary" type="button" role="tab">
                        <i class="fas fa-chart-pie me-2"></i>Accreditation Summary
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="status-by-program-tab" data-bs-toggle="tab" data-bs-target="#status-by-program" type="button" role="tab">
                        <i class="fas fa-list-ul me-2"></i>Status by Program
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="recommendations-tracker-tab" data-bs-toggle="tab" data-bs-target="#recommendations-tracker" type="button" role="tab">
                        <i class="fas fa-tasks me-2"></i>Recommendations Tracker
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="expiring-accreditations-tab" data-bs-toggle="tab" data-bs-target="#expiring-accreditations" type="button" role="tab">
                        <i class="fas fa-clock me-2"></i>Expiring Accreditations
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Accreditation Summary Tab -->
                <div class="tab-pane fade show active" id="accreditation-summary" role="tabpanel">
                    <!-- Summary Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="summaryYear" onchange="loadAccreditationSummary()">
                                    <option value="all">All Years</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                    <option value="2022">2022</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="summaryAgency" onchange="loadAccreditationSummary()">
                                    <option value="all">All Agencies</option>
                                    <option value="PAASCU">PAASCU</option>
                                    <option value="AACCUP">AACCUP</option>
                                    <option value="PACUCOA">PACUCOA</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="summaryDepartment" onchange="loadAccreditationSummary()">
                                    <option value="all">All Departments</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-primary" onclick="exportSummaryReport()">
                                    <i class="fas fa-download me-2"></i>Export Report
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Dashboard -->
                    <div class="row mb-4" id="summaryDashboard">
                        <!-- Dashboard cards will be populated by JavaScript -->
                    </div>

                    <!-- Charts Section -->
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Accreditation Levels Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <div id="levelsChart" style="height: 300px;">
                                        <!-- Chart will be rendered here -->
                                        <div class="text-center text-muted py-5">
                                            <i class="fas fa-chart-pie fa-3x mb-3"></i>
                                            <p>Chart visualization</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Programs by Agency</h5>
                                </div>
                                <div class="card-body">
                                    <div id="agencyChart" style="height: 300px;">
                                        <!-- Chart will be rendered here -->
                                        <div class="text-center text-muted py-5">
                                            <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                            <p>Chart visualization</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status by Program Tab -->
                <div class="tab-pane fade" id="status-by-program" role="tabpanel">
                    <!-- Status Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchPrograms" placeholder="Search programs..." onkeyup="searchProgramStatus()">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusDepartment" onchange="filterProgramStatus()">
                                    <option value="all">All Departments</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusLevel" onchange="filterProgramStatus()">
                                    <option value="all">All Levels</option>
                                    <option value="Level I">Level I</option>
                                    <option value="Level II">Level II</option>
                                    <option value="Level III">Level III</option>
                                    <option value="Level IV">Level IV</option>
                                    <option value="Not Accredited">Not Accredited</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="statusAgency" onchange="filterProgramStatus()">
                                    <option value="all">All Agencies</option>
                                    <option value="PAASCU">PAASCU</option>
                                    <option value="AACCUP">AACCUP</option>
                                    <option value="PACUCOA">PACUCOA</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-success" onclick="exportProgramStatusReport()">
                                    <i class="fas fa-download me-2"></i>Export Status Report
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Program Status Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-list-ul me-2"></i>Program Accreditation Status</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Department</th>
                                            <th>Current Level</th>
                                            <th>Agency</th>
                                            <th>Status</th>
                                            <th>Valid Until</th>
                                            <th>Next Milestone</th>
                                            <th>Progress</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="programStatusTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recommendations Tracker Tab -->
                <div class="tab-pane fade" id="recommendations-tracker" role="tabpanel">
                    <!-- Recommendations Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="recommendationsProgram" onchange="filterRecommendations()">
                                    <option value="all">All Programs</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="recommendationsStatus" onchange="filterRecommendations()">
                                    <option value="all">All Status</option>
                                    <option value="Open">Open</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Overdue">Overdue</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="recommendationsPriority" onchange="filterRecommendations()">
                                    <option value="all">All Priorities</option>
                                    <option value="Critical">Critical</option>
                                    <option value="High">High</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Low">Low</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="recommendationsArea" onchange="filterRecommendations()">
                                    <option value="all">All Areas</option>
                                    <option value="Area 1">Area 1</option>
                                    <option value="Area 2">Area 2</option>
                                    <option value="Area 3">Area 3</option>
                                    <option value="Area 4">Area 4</option>
                                    <option value="Area 5">Area 5</option>
                                    <option value="Area 6">Area 6</option>
                                    <option value="Area 7">Area 7</option>
                                    <option value="Area 8">Area 8</option>
                                    <option value="Area 9">Area 9</option>
                                    <option value="Area 10">Area 10</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-warning" onclick="openAddRecommendationModal()">
                                    <i class="fas fa-plus me-2"></i>Add Recommendation
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Recommendations Summary Cards -->
                    <div class="row mb-4" id="recommendationsSummary">
                        <!-- Summary cards will be populated by JavaScript -->
                    </div>

                    <!-- Recommendations Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Recommendations Tracking</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Program</th>
                                            <th>Area</th>
                                            <th>Recommendation</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Assigned To</th>
                                            <th>Due Date</th>
                                            <th>Progress</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recommendationsTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Expiring Accreditations Tab -->
                <div class="tab-pane fade" id="expiring-accreditations" role="tabpanel">
                    <!-- Expiring Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="expiringTimeframe" onchange="filterExpiringAccreditations()">
                                    <option value="all">All Timeframes</option>
                                    <option value="30">Expiring in 30 days</option>
                                    <option value="90">Expiring in 90 days</option>
                                    <option value="180">Expiring in 6 months</option>
                                    <option value="365">Expiring in 1 year</option>
                                    <option value="expired">Already Expired</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="expiringDepartment" onchange="filterExpiringAccreditations()">
                                    <option value="all">All Departments</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="expiringLevel" onchange="filterExpiringAccreditations()">
                                    <option value="all">All Levels</option>
                                    <option value="Level I">Level I</option>
                                    <option value="Level II">Level II</option>
                                    <option value="Level III">Level III</option>
                                    <option value="Level IV">Level IV</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-danger" onclick="exportExpiringReport()">
                                    <i class="fas fa-download me-2"></i>Export Alert Report
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Expiring Alerts -->
                    <div class="row mb-4" id="expiringAlerts">
                        <!-- Alert cards will be populated by JavaScript -->
                    </div>

                    <!-- Expiring Accreditations Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Accreditation Expiry Monitoring</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Department</th>
                                            <th>Current Level</th>
                                            <th>Expiry Date</th>
                                            <th>Days Remaining</th>
                                            <th>Renewal Status</th>
                                            <th>Next Action</th>
                                            <th>Priority</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="expiringTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Page-specific JavaScript for Reports
        let summaryData = {};
        let programStatusData = [];
        let recommendationsData = [];
        let expiringAccreditationsData = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeReportsData();
            loadAccreditationSummary();
            loadProgramStatus();
            loadRecommendations();
            loadExpiringAccreditations();
            populateDropdowns();
        });

        // Initialize sample data for all reports
        function initializeReportsData() {
            if (Object.keys(summaryData).length === 0) {
                summaryData = {
                    totalPrograms: 8,
                    accreditedPrograms: 6,
                    inProgressPrograms: 2,
                    levelDistribution: {
                        'Level I': 1,
                        'Level II': 2,
                        'Level III': 3,
                        'Level IV': 0,
                        'Not Accredited': 2
                    },
                    agencyDistribution: {
                        'PAASCU': 4,
                        'AACCUP': 2,
                        'PACUCOA': 0,
                        'In Process': 2
                    },
                    departmentSummary: {
                        'College of Engineering': { total: 3, accredited: 2, inProgress: 1 },
                        'College of Computer Studies': { total: 2, accredited: 2, inProgress: 0 },
                        'College of Nursing': { total: 2, accredited: 1, inProgress: 1 },
                        'College of Business': { total: 1, accredited: 1, inProgress: 0 }
                    }
                };
            }

            if (programStatusData.length === 0) {
                programStatusData = [
                    { 
                        id: 1, 
                        program: 'BS Information Technology', 
                        department: 'College of Computer Studies',
                        currentLevel: 'Level III',
                        agency: 'PAASCU',
                        status: 'Accredited',
                        validUntil: '2027-05-15',
                        nextMilestone: 'Mid-term Report',
                        progress: 85,
                        milestoneDate: '2025-05-15'
                    },
                    { 
                        id: 2, 
                        program: 'BS Computer Science', 
                        department: 'College of Computer Studies',
                        currentLevel: 'Level II',
                        agency: 'AACCUP',
                        status: 'Accredited',
                        validUntil: '2026-03-20',
                        nextMilestone: 'Annual Report',
                        progress: 90,
                        milestoneDate: '2025-03-20'
                    },
                    { 
                        id: 3, 
                        program: 'BS Civil Engineering', 
                        department: 'College of Engineering',
                        currentLevel: 'Level II',
                        agency: 'PAASCU',
                        status: 'Conditional',
                        validUntil: '2025-12-31',
                        nextMilestone: 'Compliance Report',
                        progress: 60,
                        milestoneDate: '2024-06-30'
                    },
                    { 
                        id: 4, 
                        program: 'BS Nursing', 
                        department: 'College of Nursing',
                        currentLevel: 'Not Accredited',
                        agency: 'PAASCU',
                        status: 'Application Stage',
                        validUntil: null,
                        nextMilestone: 'Submit Application',
                        progress: 25,
                        milestoneDate: '2024-04-15'
                    }
                ];
            }

            if (recommendationsData.length === 0) {
                recommendationsData = [
                    {
                        id: 1,
                        programId: 1,
                        area: 'Area 2',
                        recommendation: 'Increase faculty with PhD qualifications',
                        priority: 'High',
                        status: 'In Progress',
                        assignedTo: 'Dr. Jane Smith',
                        dueDate: '2024-06-30',
                        progress: 65,
                        description: 'At least 60% of faculty should have PhD degrees'
                    },
                    {
                        id: 2,
                        programId: 2,
                        area: 'Area 9',
                        recommendation: 'Upgrade computer laboratory equipment',
                        priority: 'Critical',
                        status: 'Open',
                        assignedTo: 'IT Manager',
                        dueDate: '2024-08-15',
                        progress: 0,
                        description: 'Update hardware and software in all computer labs'
                    },
                    {
                        id: 3,
                        programId: 1,
                        area: 'Area 5',
                        recommendation: 'Establish research publication requirements',
                        priority: 'Medium',
                        status: 'Completed',
                        assignedTo: 'Research Director',
                        dueDate: '2024-03-31',
                        progress: 100,
                        description: 'Set minimum publication requirements for faculty'
                    },
                    {
                        id: 4,
                        programId: 3,
                        area: 'Area 8',
                        recommendation: 'Improve classroom ventilation systems',
                        priority: 'High',
                        status: 'Overdue',
                        assignedTo: 'Facilities Manager',
                        dueDate: '2024-02-28',
                        progress: 30,
                        description: 'Install proper ventilation in all classrooms'
                    }
                ];
            }

            if (expiringAccreditationsData.length === 0) {
                expiringAccreditationsData = [
                    {
                        id: 1,
                        programId: 1,
                        department: 'College of Computer Studies',
                        currentLevel: 'Level III',
                        expiryDate: '2027-05-15',
                        renewalStatus: 'On Track',
                        nextAction: 'Mid-term Report',
                        priority: 'Medium',
                        actionDate: '2025-05-15'
                    },
                    {
                        id: 2,
                        programId: 2,
                        department: 'College of Computer Studies',
                        currentLevel: 'Level II',
                        expiryDate: '2026-03-20',
                        renewalStatus: 'Planning',
                        nextAction: 'Start Renewal Process',
                        priority: 'Medium',
                        actionDate: '2025-03-20'
                    },
                    {
                        id: 3,
                        programId: 3,
                        department: 'College of Engineering',
                        currentLevel: 'Level II',
                        expiryDate: '2025-12-31',
                        renewalStatus: 'At Risk',
                        nextAction: 'Submit Compliance Report',
                        priority: 'Critical',
                        actionDate: '2024-06-30'
                    }
                ];
            }
        }

        // Accreditation Summary Functions
        function loadAccreditationSummary() {
            loadSummaryDashboard();
            generateCharts();
        }

        function loadSummaryDashboard() {
            const container = document.getElementById('summaryDashboard');
            const accreditationRate = Math.round((summaryData.accreditedPrograms / summaryData.totalPrograms) * 100);
            
            container.innerHTML = `
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card bg-primary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0">${summaryData.totalPrograms}</h4>
                                    <p class="mb-0">Total Programs</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <i class="fas fa-graduation-cap fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card bg-success text-white h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0">${summaryData.accreditedPrograms}</h4>
                                    <p class="mb-0">Accredited Programs</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <i class="fas fa-certificate fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card bg-warning text-white h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0">${summaryData.inProgressPrograms}</h4>
                                    <p class="mb-0">In Progress</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card bg-info text-white h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0">${accreditationRate}%</h4>
                                    <p class="mb-0">Accreditation Rate</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <i class="fas fa-percentage fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateCharts() {
            // Placeholder for chart generation
            // In a real implementation, you would use a charting library like Chart.js
            showToast('Charts functionality ready for implementation with Chart.js or similar library');
        }

        // Program Status Functions
        function loadProgramStatus() {
            const tbody = document.getElementById('programStatusTableBody');
            tbody.innerHTML = '';

            programStatusData.forEach(program => {
                const daysUntilExpiry = program.validUntil ? Math.ceil((new Date(program.validUntil) - new Date()) / (1000 * 60 * 60 * 24)) : null;
                const daysUntilMilestone = Math.ceil((new Date(program.milestoneDate) - new Date()) / (1000 * 60 * 60 * 24));
                
                const row = `
                    <tr>
                        <td><strong>${program.program}</strong></td>
                        <td>${program.department}</td>
                        <td><span class="badge ${getLevelBadgeClass(program.currentLevel)}">${program.currentLevel}</span></td>
                        <td><span class="badge bg-info">${program.agency}</span></td>
                        <td><span class="badge ${getStatusBadgeClass(program.status)}">${program.status}</span></td>
                        <td>
                            ${program.validUntil ? formatDate(program.validUntil) : 'N/A'}
                            ${daysUntilExpiry ? `<br><small class="text-muted">${daysUntilExpiry} days remaining</small>` : ''}
                        </td>
                        <td>
                            ${program.nextMilestone}
                            <br><small class="text-muted">Due: ${formatDate(program.milestoneDate)} (${daysUntilMilestone} days)</small>
                        </td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${getProgressBarClass(program.progress)}" 
                                     style="width: ${program.progress}%" 
                                     title="${program.progress}% Complete">
                                    ${program.progress}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewProgramDetails(${program.id})">
                                <i class="fas fa-eye"></i> View
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="updateProgramStatus(${program.id})">
                                <i class="fas fa-edit"></i> Update
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Recommendations Functions
        function loadRecommendations() {
            loadRecommendationsSummary();
            loadRecommendationsTable();
        }

        function loadRecommendationsSummary() {
            const container = document.getElementById('recommendationsSummary');
            const openCount = recommendationsData.filter(r => r.status === 'Open').length;
            const inProgressCount = recommendationsData.filter(r => r.status === 'In Progress').length;
            const completedCount = recommendationsData.filter(r => r.status === 'Completed').length;
            const overdueCount = recommendationsData.filter(r => r.status === 'Overdue').length;
            
            container.innerHTML = `
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-0">${openCount}</h4>
                            <p class="mb-0">Open</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-0">${inProgressCount}</h4>
                            <p class="mb-0">In Progress</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-0">${completedCount}</h4>
                            <p class="mb-0">Completed</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-0">${overdueCount}</h4>
                            <p class="mb-0">Overdue</p>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadRecommendationsTable() {
            const tbody = document.getElementById('recommendationsTableBody');
            tbody.innerHTML = '';

            recommendationsData.forEach(rec => {
                const program = getEntityById(programs, rec.programId);
                const daysUntilDue = Math.ceil((new Date(rec.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
                
                const row = `
                    <tr>
                        <td>${rec.id}</td>
                        <td><strong>${program ? program.name : 'Unknown'}</strong></td>
                        <td><span class="badge bg-info">${rec.area}</span></td>
                        <td>
                            <strong>${rec.recommendation}</strong>
                            <br><small class="text-muted">${rec.description}</small>
                        </td>
                        <td><span class="badge ${getPriorityBadgeClass(rec.priority)}">${rec.priority}</span></td>
                        <td><span class="badge ${getStatusBadgeClass(rec.status)}">${rec.status}</span></td>
                        <td>${rec.assignedTo}</td>
                        <td>
                            ${formatDate(rec.dueDate)}
                            <br><small class="text-muted ${daysUntilDue < 0 ? 'text-danger' : daysUntilDue < 7 ? 'text-warning' : 'text-muted'}">
                                ${daysUntilDue < 0 ? `${Math.abs(daysUntilDue)} days overdue` : `${daysUntilDue} days remaining`}
                            </small>
                        </td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${getProgressBarClass(rec.progress)}" 
                                     style="width: ${rec.progress}%" 
                                     title="${rec.progress}% Complete">
                                    ${rec.progress}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewRecommendation(${rec.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="updateRecommendation(${rec.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Expiring Accreditations Functions
        function loadExpiringAccreditations() {
            loadExpiringAlerts();
            loadExpiringTable();
        }

        function loadExpiringAlerts() {
            const container = document.getElementById('expiringAlerts');
            const thirtyDays = expiringAccreditationsData.filter(acc => {
                const daysRemaining = Math.ceil((new Date(acc.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
                return daysRemaining <= 30 && daysRemaining > 0;
            }).length;
            
            const ninetyDays = expiringAccreditationsData.filter(acc => {
                const daysRemaining = Math.ceil((new Date(acc.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
                return daysRemaining <= 90 && daysRemaining > 30;
            }).length;
            
            const expired = expiringAccreditationsData.filter(acc => {
                const daysRemaining = Math.ceil((new Date(acc.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
                return daysRemaining <= 0;
            }).length;
            
            const atRisk = expiringAccreditationsData.filter(acc => acc.renewalStatus === 'At Risk').length;
            
            container.innerHTML = `
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-0">${thirtyDays}</h4>
                            <p class="mb-0">Expiring in 30 Days</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-0">${ninetyDays}</h4>
                            <p class="mb-0">Expiring in 90 Days</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-0">${expired}</h4>
                            <p class="mb-0">Already Expired</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-0">${atRisk}</h4>
                            <p class="mb-0">At Risk</p>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadExpiringTable() {
            const tbody = document.getElementById('expiringTableBody');
            tbody.innerHTML = '';

            expiringAccreditationsData.forEach(acc => {
                const program = getEntityById(programs, acc.programId);
                const daysRemaining = Math.ceil((new Date(acc.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
                const daysUntilAction = Math.ceil((new Date(acc.actionDate) - new Date()) / (1000 * 60 * 60 * 24));
                
                const row = `
                    <tr class="${daysRemaining <= 30 ? 'table-danger' : daysRemaining <= 90 ? 'table-warning' : ''}">
                        <td><strong>${program ? program.name : 'Unknown'}</strong></td>
                        <td>${acc.department}</td>
                        <td><span class="badge ${getLevelBadgeClass(acc.currentLevel)}">${acc.currentLevel}</span></td>
                        <td>${formatDate(acc.expiryDate)}</td>
                        <td>
                            <span class="badge ${getDaysRemainingBadgeClass(daysRemaining)}">
                                ${daysRemaining > 0 ? `${daysRemaining} days` : `Expired ${Math.abs(daysRemaining)} days ago`}
                            </span>
                        </td>
                        <td><span class="badge ${getRenewalStatusBadgeClass(acc.renewalStatus)}">${acc.renewalStatus}</span></td>
                        <td>
                            ${acc.nextAction}
                            <br><small class="text-muted">Due: ${formatDate(acc.actionDate)}</small>
                        </td>
                        <td><span class="badge ${getPriorityBadgeClass(acc.priority)}">${acc.priority}</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewExpiringDetails(${acc.id})">
                                <i class="fas fa-eye"></i> View
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="initiateRenewal(${acc.id})">
                                <i class="fas fa-refresh"></i> Renew
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Helper functions
        function getLevelBadgeClass(level) {
            switch(level) {
                case 'Level IV': return 'bg-success';
                case 'Level III': return 'bg-primary';
                case 'Level II': return 'bg-info';
                case 'Level I': return 'bg-warning';
                case 'Not Accredited': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function getStatusBadgeClass(status) {
            switch(status) {
                case 'Accredited': 
                case 'Completed': return 'bg-success';
                case 'In Progress': 
                case 'Application Stage': return 'bg-primary';
                case 'Conditional': 
                case 'Open': return 'bg-warning';
                case 'Overdue': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        function getPriorityBadgeClass(priority) {
            switch(priority) {
                case 'Critical': return 'bg-danger';
                case 'High': return 'bg-warning';
                case 'Medium': return 'bg-info';
                case 'Low': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function getProgressBarClass(progress) {
            if (progress >= 80) return 'bg-success';
            if (progress >= 50) return 'bg-info';
            if (progress >= 25) return 'bg-warning';
            return 'bg-danger';
        }

        function getDaysRemainingBadgeClass(days) {
            if (days <= 0) return 'bg-dark';
            if (days <= 30) return 'bg-danger';
            if (days <= 90) return 'bg-warning';
            return 'bg-success';
        }

        function getRenewalStatusBadgeClass(status) {
            switch(status) {
                case 'On Track': return 'bg-success';
                case 'Planning': return 'bg-info';
                case 'At Risk': return 'bg-danger';
                case 'Behind Schedule': return 'bg-warning';
                default: return 'bg-secondary';
            }
        }

        // Populate dropdowns
        function populateDropdowns() {
            const departmentSelects = ['summaryDepartment', 'statusDepartment', 'expiringDepartment'];
            departmentSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const currentValue = select.value;
                    select.innerHTML = '<option value="all">All Departments</option>';
                    departments.forEach(dept => {
                        select.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
                    });
                    if (currentValue) select.value = currentValue;
                }
            });

            const programSelects = ['recommendationsProgram'];
            programSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const currentValue = select.value;
                    select.innerHTML = '<option value="all">All Programs</option>';
                    programs.forEach(program => {
                        select.innerHTML += `<option value="${program.id}">${program.name}</option>`;
                    });
                    if (currentValue) select.value = currentValue;
                }
            });
        }

        // Export functions
        function exportSummaryReport() { 
            // Generate CSV data for summary report
            const csvData = `Report Type,Summary Report - Accreditation Overview
Generated Date,${formatDate(new Date().toISOString().split('T')[0])}

Metric,Value
Total Programs,${summaryData.totalPrograms}
Accredited Programs,${summaryData.accreditedPrograms}
In Progress Programs,${summaryData.inProgressPrograms}
Accreditation Rate,${Math.round((summaryData.accreditedPrograms / summaryData.totalPrograms) * 100)}%

Program Details:
Program,Department,Current Level,Status,Valid Until
${programStatusData.map(program => 
    `${program.program},${program.department},${program.currentLevel},${program.status},${program.validUntil || 'N/A'}`
).join('\n')}`;
            
            downloadCSV(csvData, 'Accreditation_Summary_Report.csv');
            showToast('Summary report exported successfully!');
        }

        function exportProgramStatusReport() { 
            // Generate CSV data for program status report
            const csvData = `Program Status Report
Generated Date,${formatDate(new Date().toISOString().split('T')[0])}

Program,Department,Current Level,Agency,Status,Valid Until,Next Milestone,Progress %
${programStatusData.map(program => {
                const daysUntilExpiry = program.validUntil ? Math.ceil((new Date(program.validUntil) - new Date()) / (1000 * 60 * 60 * 24)) : 'N/A';
                const daysUntilMilestone = Math.ceil((new Date(program.milestoneDate) - new Date()) / (1000 * 60 * 60 * 24));
                return `${program.program},${program.department},${program.currentLevel},${program.agency},${program.status},${program.validUntil || 'N/A'} (${daysUntilExpiry} days),${program.nextMilestone} (${daysUntilMilestone} days),${program.progress}%`;
            }).join('\n')}`;
            
            downloadCSV(csvData, 'Program_Status_Report.csv');
            showToast('Program status report exported successfully!');
        }

        function exportExpiringReport() { 
            // Generate CSV data for expiring accreditations report
            const csvData = `Expiring Accreditations Alert Report
Generated Date,${formatDate(new Date().toISOString().split('T')[0])}

Program,Department,Current Level,Expiry Date,Days Remaining,Renewal Status,Next Action,Action Date,Priority
${expiringAccreditationsData.map(acc => {
                const daysRemaining = Math.ceil((new Date(acc.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
                const program = getEntityById(programs, acc.programId);
                return `${program ? program.name : 'Unknown'},${acc.department},${acc.currentLevel},${acc.expiryDate},${daysRemaining},${acc.renewalStatus},${acc.nextAction},${acc.actionDate},${acc.priority}`;
            }).join('\n')}`;
            
            downloadCSV(csvData, 'Expiring_Accreditations_Report.csv');
            showToast('Expiring accreditations report exported successfully!');
        }

        // Helper function to download CSV
        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // Modal functions
        function openAddRecommendationModal() { 
            // Create a new recommendation using a simplified approach
            const programId = prompt('Enter Program ID (1-7):');
            if (!programId || isNaN(programId)) {
                showToast('Invalid Program ID', 'error');
                return;
            }

            const program = getEntityById(programs, parseInt(programId));
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const area = prompt('Enter Area (e.g., Area 1, Area 2, etc.):', 'Area 1');
            if (!area) return;

            const recommendation = prompt('Enter recommendation title:', 'Improve Faculty Development');
            if (!recommendation) return;

            const description = prompt('Enter recommendation description:', 'Enhance faculty development programs and training opportunities');
            if (!description) return;

            const priority = prompt('Enter priority (Critical/High/Medium/Low):', 'Medium');
            if (!priority) return;

            const assignedTo = prompt('Enter assigned person:', 'Department Head');
            if (!assignedTo) return;

            const dueDate = prompt('Enter due date (YYYY-MM-DD):', '2024-12-31');
            if (!dueDate) return;

            const newRecommendation = {
                id: generateId(recommendationsData),
                programId: parseInt(programId),
                area: area,
                recommendation: recommendation,
                description: description,
                priority: priority,
                status: 'Open',
                assignedTo: assignedTo,
                dueDate: dueDate,
                progress: 0
            };

            recommendationsData.push(newRecommendation);
            loadRecommendations();
            showToast('Recommendation added successfully!');
        }

        // Action functions (fully implemented)
        function viewProgramDetails(id) { 
            const program = programStatusData.find(p => p.id === id);
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const daysUntilExpiry = program.validUntil ? Math.ceil((new Date(program.validUntil) - new Date()) / (1000 * 60 * 60 * 24)) : 'N/A';
            const daysUntilMilestone = Math.ceil((new Date(program.milestoneDate) - new Date()) / (1000 * 60 * 60 * 24));
            
            const details = `Program Details:
            
Program: ${program.program}
Department: ${program.department}
Current Level: ${program.currentLevel}
Accrediting Agency: ${program.agency}
Status: ${program.status}
Progress: ${program.progress}%

Validity Information:
Valid Until: ${program.validUntil || 'Not Applicable'}
Days Remaining: ${daysUntilExpiry}

Next Steps:
Next Milestone: ${program.nextMilestone}
Milestone Date: ${program.milestoneDate}
Days Until Milestone: ${daysUntilMilestone}`;
            
            alert(details);
        }

        function updateProgramStatus(id) { 
            const program = programStatusData.find(p => p.id === id);
            if (!program) {
                showToast('Program not found', 'error');
                return;
            }

            const newProgress = Math.min(100, program.progress + 20);
            program.progress = newProgress;
            
            // Update status based on progress
            if (newProgress >= 90) {
                program.status = 'Ready for Review';
            } else if (newProgress >= 70) {
                program.status = 'Documentation Complete';
            } else if (newProgress >= 50) {
                program.status = 'In Progress';
            } else if (newProgress >= 25) {
                program.status = 'Planning Stage';
            }
            
            loadProgramStatus();
            showToast(`${program.program} progress updated to ${newProgress}%!`);
        }

        function viewRecommendation(id) { 
            const rec = recommendationsData.find(r => r.id === id);
            if (!rec) {
                showToast('Recommendation not found', 'error');
                return;
            }

            const program = getEntityById(programs, rec.programId);
            const daysUntilDue = Math.ceil((new Date(rec.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
            
            const details = `Recommendation Details:
            
ID: ${rec.id}
Program: ${program ? program.name : 'Unknown'}
Area: ${rec.area}
Priority: ${rec.priority}
Status: ${rec.status}
Progress: ${rec.progress}%

Recommendation: ${rec.recommendation}
Description: ${rec.description}

Assignment Information:
Assigned To: ${rec.assignedTo}
Due Date: ${rec.dueDate}
Days Until Due: ${daysUntilDue < 0 ? `${Math.abs(daysUntilDue)} days overdue` : `${daysUntilDue} days remaining`}`;
            
            alert(details);
        }

        function updateRecommendation(id) { 
            const rec = recommendationsData.find(r => r.id === id);
            if (!rec) {
                showToast('Recommendation not found', 'error');
                return;
            }

            const newProgress = Math.min(100, rec.progress + 25);
            rec.progress = newProgress;
            
            // Update status based on progress
            if (newProgress >= 100) {
                rec.status = 'Completed';
            } else if (newProgress >= 75) {
                rec.status = 'Nearly Complete';
            } else if (newProgress >= 25) {
                rec.status = 'In Progress';
            }
            
            // Check if overdue
            const daysUntilDue = Math.ceil((new Date(rec.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
            if (daysUntilDue < 0 && rec.status !== 'Completed') {
                rec.status = 'Overdue';
            }
            
            loadRecommendations();
            showToast(`Recommendation ${id} progress updated to ${newProgress}%!`);
        }

        function viewExpiringDetails(id) { 
            const acc = expiringAccreditationsData.find(a => a.id === id);
            if (!acc) {
                showToast('Accreditation record not found', 'error');
                return;
            }

            const program = getEntityById(programs, acc.programId);
            const daysRemaining = Math.ceil((new Date(acc.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
            const daysUntilAction = Math.ceil((new Date(acc.actionDate) - new Date()) / (1000 * 60 * 60 * 24));
            
            const details = `Expiring Accreditation Details:
            
Program: ${program ? program.name : 'Unknown'}
Department: ${acc.department}
Current Level: ${acc.currentLevel}
Priority: ${acc.priority}

Expiry Information:
Expiry Date: ${acc.expiryDate}
Days Remaining: ${daysRemaining > 0 ? `${daysRemaining} days` : `Expired ${Math.abs(daysRemaining)} days ago`}
Renewal Status: ${acc.renewalStatus}

Action Required:
Next Action: ${acc.nextAction}
Action Date: ${acc.actionDate}
Days Until Action: ${daysUntilAction}`;
            
            alert(details);
        }

        function initiateRenewal(id) { 
            const acc = expiringAccreditationsData.find(a => a.id === id);
            if (!acc) {
                showToast('Accreditation record not found', 'error');
                return;
            }

            const confirmed = confirm(`Initiate renewal process for ${acc.department}?\n\nThis will start the accreditation renewal workflow.`);
            if (confirmed) {
                acc.renewalStatus = 'In Progress';
                acc.nextAction = 'Submit Renewal Application';
                acc.priority = 'High';
                
                // Add 30 days to action date
                const newDate = new Date(acc.actionDate);
                newDate.setDate(newDate.getDate() + 30);
                acc.actionDate = newDate.toISOString().split('T')[0];
                
                loadExpiringAccreditations();
                showToast('Renewal process initiated successfully!');
            }
        }

        // Search and filter functions
        function searchProgramStatus() {
            const searchTerm = document.getElementById('searchPrograms').value.toLowerCase();
            const rows = document.querySelectorAll('#programStatusTableBody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }

        function filterProgramStatus() { loadProgramStatus(); }
        function filterRecommendations() { loadRecommendations(); }
        function filterExpiringAccreditations() { loadExpiringAccreditations(); }
    </script>
</body>
</html> 