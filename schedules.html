<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accreditation Schedules - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid">
                    <h1><i class="fas fa-calendar me-3"></i>Accreditation Schedules</h1>
                    <p>Manage accreditation visit schedules and timelines for programs and departments</p>
                </div>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-schedules-tab" data-bs-toggle="tab" data-bs-target="#all-schedules" type="button" role="tab">
                        <i class="fas fa-list me-2"></i>All Schedules
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="visit-calendar-tab" data-bs-toggle="tab" data-bs-target="#visit-calendar" type="button" role="tab">
                        <i class="fas fa-calendar-alt me-2"></i>Visit Calendar
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="deadlines-tab" data-bs-toggle="tab" data-bs-target="#deadlines" type="button" role="tab">
                        <i class="fas fa-clock me-2"></i>Deadlines & Timelines
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="evaluation-periods-tab" data-bs-toggle="tab" data-bs-target="#evaluation-periods" type="button" role="tab">
                        <i class="fas fa-calendar-check me-2"></i>Evaluation Periods
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- All Schedules Tab -->
                <div class="tab-pane fade show active" id="all-schedules" role="tabpanel">
                    <!-- Schedule Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchSchedules" placeholder="Search schedules..." onkeyup="searchSchedules()">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="scheduleStatusFilter" onchange="filterSchedules()">
                                    <option value="all">All Status</option>
                                    <option value="Preparation">Preparation</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Postponed">Postponed</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="schedulePriorityFilter" onchange="filterSchedules()">
                                    <option value="all">All Priorities</option>
                                    <option value="Urgent">Urgent</option>
                                    <option value="High">High</option>
                                    <option value="Normal">Normal</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="scheduleDepartmentFilter" onchange="filterSchedules()">
                                    <option value="all">All Departments</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-nd-green" onclick="openAddScheduleModal()">
                                    <i class="fas fa-plus me-2"></i>Add Schedule
                                </button>
                                <button class="btn btn-outline-secondary ms-2" onclick="refreshSchedulesList()">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Schedules Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Accreditation Schedules</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Program</th>
                                            <th>Department</th>
                                            <th>Agency</th>
                                            <th>Level</th>
                                            <th>Visit Date</th>
                                            <th>Status</th>
                                            <th>Priority</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="allSchedulesTableBody">
                                        <!-- Schedules will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Visit Calendar Tab -->
                <div class="tab-pane fade" id="visit-calendar" role="tabpanel">
                    <!-- Calendar Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="calendarView" onchange="changeCalendarView()">
                                    <option value="month">Month View</option>
                                    <option value="week">Week View</option>
                                    <option value="list">List View</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="month" class="form-control" id="calendarMonth" onchange="changeCalendarMonth()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="calendarAgencyFilter" onchange="filterCalendarByAgency()">
                                    <option value="all">All Agencies</option>
                                    <option value="PAASCU">PAASCU</option>
                                    <option value="AACCUP">AACCUP</option>
                                    <option value="PACUCOA">PACUCOA</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-nd-green" onclick="openAddScheduleModal()">
                                    <i class="fas fa-plus me-2"></i>Schedule Visit
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Calendar Display -->
                    <div class="card">
                        <div class="card-header bg-nd-green text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-calendar me-2"></i>Visit Calendar</h5>
                        </div>
                        <div class="card-body">
                            <div id="calendarContainer">
                                <!-- Calendar will be rendered here -->
                                <div class="row" id="calendarGrid">
                                    <!-- Calendar grid will be populated by JavaScript -->
                                </div>
                            </div>
                            
                            <!-- List View -->
                            <div id="calendarListView" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Program</th>
                                                <th>Agency</th>
                                                <th>Visit Type</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="calendarListBody">
                                            <!-- List items will be populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deadlines & Timelines Tab -->
                <div class="tab-pane fade" id="deadlines" role="tabpanel">
                    <!-- Deadline Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="deadlineFilter" onchange="filterDeadlines()">
                                    <option value="all">All Deadlines</option>
                                    <option value="overdue">Overdue</option>
                                    <option value="upcoming">Upcoming (30 days)</option>
                                    <option value="this-month">This Month</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="deadlineTypeFilter" onchange="filterDeadlines()">
                                    <option value="all">All Types</option>
                                    <option value="Document Submission">Document Submission</option>
                                    <option value="Self-Survey">Self-Survey</option>
                                    <option value="Visit Preparation">Visit Preparation</option>
                                    <option value="Report Submission">Report Submission</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="deadlinePriorityFilter" onchange="filterDeadlines()">
                                    <option value="all">All Priorities</option>
                                    <option value="Critical">Critical</option>
                                    <option value="High">High</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Low">Low</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-primary" onclick="openAddDeadlineModal()">
                                    <i class="fas fa-plus me-2"></i>Add Deadline
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Deadlines Cards -->
                    <div class="row" id="deadlinesContainer">
                        <!-- Deadline cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Evaluation Periods Tab -->
                <div class="tab-pane fade" id="evaluation-periods" role="tabpanel">
                    <!-- Evaluation Period Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchEvaluationPeriods" placeholder="Search evaluation periods..." onkeyup="searchEvaluationPeriods()">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="evaluationStatusFilter" onchange="filterEvaluationPeriods()">
                                    <option value="all">All Status</option>
                                    <option value="Planned">Planned</option>
                                    <option value="Active">Active</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Postponed">Postponed</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="evaluationYearFilter" onchange="filterEvaluationPeriods()">
                                    <option value="all">All Years</option>
                                    <option value="2024">2024</option>
                                    <option value="2025">2025</option>
                                    <option value="2026">2026</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-success" onclick="openAddEvaluationPeriodModal()">
                                    <i class="fas fa-plus me-2"></i>Add Period
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Evaluation Periods Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-calendar-check me-2"></i>Evaluation Periods</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Period Name</th>
                                            <th>Program</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Duration</th>
                                            <th>Status</th>
                                            <th>Progress</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="evaluationPeriodsTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Schedule Modal -->
    <div class="modal fade" id="addScheduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addScheduleForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Department <span class="text-danger">*</span></label>
                                    <select class="form-select" id="scheduleDepartment" required onchange="loadProgramsByDepartment()">
                                        <option value="">Select Department</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Program <span class="text-danger">*</span></label>
                                    <select class="form-select" id="scheduleProgram" required>
                                        <option value="">Select Program</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Accrediting Agency <span class="text-danger">*</span></label>
                                    <select class="form-select" id="scheduleAgency" required>
                                        <option value="">Select Agency</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Level <span class="text-danger">*</span></label>
                                    <select class="form-select" id="scheduleLevel" required>
                                        <option value="">Select Level</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Cycle <span class="text-danger">*</span></label>
                                    <select class="form-select" id="scheduleCycle" required>
                                        <option value="">Select Cycle</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Coverage Period <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="scheduleCoveragePeriod" placeholder="e.g., AY 2025-2027" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Visit Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="scheduleVisitDate" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="scheduleStatus" required>
                                        <option value="">Select Status</option>
                                        <option value="Preparation">Preparation</option>
                                        <option value="In Progress">In Progress</option>
                                        <option value="Completed">Completed</option>
                                        <option value="Postponed">Postponed</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <select class="form-select" id="schedulePriority">
                                        <option value="Normal">Normal</option>
                                        <option value="High">High</option>
                                        <option value="Urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="scheduleNotes" rows="3" placeholder="Additional notes or comments about the accreditation schedule"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveSchedule()">
                        <i class="fas fa-save me-2"></i>Save Schedule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Schedule Modal -->
    <div class="modal fade" id="editScheduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editScheduleForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Department <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleDepartment" required onchange="loadEditProgramsByDepartment()">
                                        <option value="">Select Department</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Program <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleProgram" required>
                                        <option value="">Select Program</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Accrediting Agency <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleAgency" required>
                                        <option value="">Select Agency</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Level <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleLevel" required>
                                        <option value="">Select Level</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Cycle <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleCycle" required>
                                        <option value="">Select Cycle</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Coverage Period <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editScheduleCoveragePeriod" placeholder="e.g., AY 2025-2027" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Visit Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="editScheduleVisitDate" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editScheduleStatus" required>
                                        <option value="">Select Status</option>
                                        <option value="Preparation">Preparation</option>
                                        <option value="In Progress">In Progress</option>
                                        <option value="Completed">Completed</option>
                                        <option value="Postponed">Postponed</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority</label>
                                    <select class="form-select" id="editSchedulePriority">
                                        <option value="Normal">Normal</option>
                                        <option value="High">High</option>
                                        <option value="Urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="editScheduleNotes" rows="3" placeholder="Additional notes or comments about the accreditation schedule"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="updateSchedule()">
                        <i class="fas fa-save me-2"></i>Update Schedule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Schedule Details Modal -->
    <div class="modal fade" id="viewScheduleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Schedule Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Department:</label>
                                <p id="viewDepartment" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Program:</label>
                                <p id="viewProgram" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Agency:</label>
                                <p id="viewAgency" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Level:</label>
                                <p id="viewLevel" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Cycle:</label>
                                <p id="viewCycle" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Coverage Period:</label>
                                <p id="viewCoveragePeriod" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Visit Date:</label>
                                <p id="viewVisitDate" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <p id="viewStatus" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Priority:</label>
                                <p id="viewPriority" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Notes:</label>
                                <p id="viewNotes" class="form-control-plaintext">-</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Visit Modal -->
    <div class="modal fade" id="addVisitModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Schedule Visit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addVisitForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Program <span class="text-danger">*</span></label>
                                    <select class="form-select" id="visitProgram" required>
                                        <option value="">Select Program</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Agency <span class="text-danger">*</span></label>
                                    <select class="form-select" id="visitAgency" required>
                                        <option value="">Select Agency</option>
                                        <option value="PAASCU">PAASCU</option>
                                        <option value="AACCUP">AACCUP</option>
                                        <option value="PACUCOA">PACUCOA</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Visit Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="visitType" required>
                                        <option value="">Select Type</option>
                                        <option value="Initial Visit">Initial Visit</option>
                                        <option value="Follow-up Visit">Follow-up Visit</option>
                                        <option value="Re-accreditation">Re-accreditation</option>
                                        <option value="Special Visit">Special Visit</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Visit Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="visitDate" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Start Time</label>
                                    <input type="time" class="form-control" id="visitStartTime" value="08:00">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">End Time</label>
                                    <input type="time" class="form-control" id="visitEndTime" value="17:00">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" id="visitNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="addVisit()">Schedule Visit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Deadline Modal -->
    <div class="modal fade" id="addDeadlineModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add Deadline</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addDeadlineForm">
                        <div class="mb-3">
                            <label class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="deadlineTitle" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="deadlineType" required>
                                <option value="">Select Type</option>
                                <option value="Document Submission">Document Submission</option>
                                <option value="Self-Survey">Self-Survey</option>
                                <option value="Visit Preparation">Visit Preparation</option>
                                <option value="Report Submission">Report Submission</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Due Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="deadlineDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Priority <span class="text-danger">*</span></label>
                                    <select class="form-select" id="deadlinePriority" required>
                                        <option value="">Select Priority</option>
                                        <option value="Low">Low</option>
                                        <option value="Medium">Medium</option>
                                        <option value="High">High</option>
                                        <option value="Critical">Critical</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Program</label>
                            <select class="form-select" id="deadlineProgram">
                                <option value="">All Programs</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="deadlineDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="addDeadline()">Add Deadline</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Evaluation Period Modal -->
    <div class="modal fade" id="addEvaluationPeriodModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add Evaluation Period</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addEvaluationPeriodForm">
                        <div class="mb-3">
                            <label class="form-label">Period Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="evaluationPeriodName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Program <span class="text-danger">*</span></label>
                            <select class="form-select" id="evaluationPeriodProgram" required>
                                <option value="">Select Program</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Start Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="evaluationPeriodStartDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">End Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="evaluationPeriodEndDate" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" id="evaluationPeriodStatus">
                                <option value="Planned">Planned</option>
                                <option value="Active">Active</option>
                                <option value="Completed">Completed</option>
                                <option value="Postponed">Postponed</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="evaluationPeriodDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="addEvaluationPeriod()">Add Period</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Page-specific JavaScript for Schedules
        let currentEditingScheduleId = null;
        let visitCalendarData = [];
        let deadlinesData = [];
        let evaluationPeriodsData = [];
        let currentCalendarDate = new Date();

        // Helper function to fetch programs via AJAX
async function fetchPrograms() {
    try {
        const response = await fetch('programs.json');
        if (!response.ok) throw new Error('Failed to load programs');
        const programsData = await response.json();
        window.programs = programsData;
    } catch (e) {
        console.error('Error loading programs:', e);
        window.programs = [];
    }
}

// Helper function to fetch cycles via AJAX
async function fetchCycles() {
    try {
        const response = await fetch('cycles.json');
        if (!response.ok) throw new Error('Failed to load cycles');
        const cyclesData = await response.json();
        window.cycles = cyclesData;
    } catch (e) {
        console.error('Error loading cycles:', e);
        window.cycles = [];
    }
}

// Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            initializeScheduleData();
            // Fetch programs and cycles before other loads
            await fetchPrograms();
            await fetchCycles();
            await loadAllSchedules();
            loadVisitCalendar();
            loadDeadlines();
            loadEvaluationPeriods();
            await populateDropdowns(); // now async
            populateScheduleFilters();
            setCurrentMonth();
        });

        // Initialize sample data
        function initializeScheduleData() {
            if (visitCalendarData.length === 0) {
                visitCalendarData = [
                    { id: 1, programId: 1, agency: 'PAASCU', visitType: 'Initial Visit', date: '2025-01-15', startTime: '08:00', endTime: '17:00', status: 'Scheduled', notes: 'Full accreditation visit' },
                    { id: 2, programId: 2, agency: 'AACCUP', visitType: 'Re-accreditation', date: '2025-02-20', startTime: '09:00', endTime: '16:00', status: 'Scheduled', notes: 'Re-accreditation cycle' },
                    { id: 3, programId: 3, agency: 'PAASCU', visitType: 'Follow-up Visit', date: '2025-03-10', startTime: '08:30', endTime: '15:30', status: 'Scheduled', notes: 'Follow-up on recommendations' }
                ];
            }

            if (deadlinesData.length === 0) {
                deadlinesData = [
                    { id: 1, title: 'Submit Self-Survey Report', type: 'Report Submission', dueDate: '2025-01-05', priority: 'Critical', programId: 1, description: 'Complete and submit self-survey report', status: 'Pending' },
                    { id: 2, title: 'Upload Faculty Documents', type: 'Document Submission', dueDate: '2025-01-10', priority: 'High', programId: 2, description: 'Upload all faculty credentials and development records', status: 'In Progress' },
                    { id: 3, title: 'Prepare Visit Materials', type: 'Visit Preparation', dueDate: '2025-01-12', priority: 'High', programId: 1, description: 'Prepare all materials for accreditation visit', status: 'Pending' },
                    { id: 4, title: 'Document Review Completion', type: 'Document Submission', dueDate: '2024-12-28', priority: 'Critical', programId: 3, description: 'Complete review of all submitted documents', status: 'Overdue' }
                ];
            }

            if (evaluationPeriodsData.length === 0) {
                evaluationPeriodsData = [
                    { id: 1, name: 'BSIT Initial Evaluation', programId: 1, startDate: '2025-01-01', endDate: '2025-06-30', status: 'Active', progress: 45, description: 'Initial evaluation period for BSIT program' },
                    { id: 2, name: 'BSCS Re-evaluation', programId: 2, startDate: '2025-02-01', endDate: '2025-08-31', status: 'Planned', progress: 0, description: 'Re-evaluation period for BSCS program' },
                    { id: 3, name: 'BSN Level II Assessment', programId: 3, startDate: '2024-09-01', endDate: '2024-12-31', status: 'Completed', progress: 100, description: 'Level II assessment for BSN program' }
                ];
            }
        }

        // Populate dropdowns for forms (async fetch cycles)
        async function populateDropdowns() {
            // Populate departments (existing logic)
            const departmentSelects = ['scheduleDepartment', 'editScheduleDepartment'];
            departmentSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    select.innerHTML = '<option value="">Select Department</option>';
                    departments.forEach(dept => {
                        select.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
                    });
                }
            });

            // Populate agencies (existing logic - unchanged)
            const agencySelects = ['scheduleAgency', 'editScheduleAgency'];
            agencySelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    select.innerHTML = '<option value="">Select Agency</option>';
                    agencies.forEach(agency => {
                        select.innerHTML += `<option value="${agency.id}">${agency.name}</option>`;
                    });
                }
            });

            // Populate levels (existing logic - unchanged)
            const levelSelects = ['scheduleLevel', 'editScheduleLevel'];
            levelSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    select.innerHTML = '<option value="">Select Level</option>';
                    levels.forEach(level => {
                        select.innerHTML += `<option value="${level.id}">${level.level}</option>`;
                    });
                }
            });

            // Populate cycles
            const cycleSelects = ['scheduleCycle', 'editScheduleCycle'];
            cycleSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    select.innerHTML = '<option value="">Select Cycle</option>';
                    cycles.forEach(cycle => {
                        select.innerHTML += `<option value="${cycle.id}">${cycle.type}</option>`;
                    });
                }
            });
        }

        // Load programs by department
        function loadProgramsByDepartment() {
            const departmentId = document.getElementById('scheduleDepartment').value;
            const programSelect = document.getElementById('scheduleProgram');
            
            programSelect.innerHTML = '<option value="">Select Program</option>';
            
            if (departmentId) {
                const departmentPrograms = programs.filter(p => p.departmentId == departmentId);
                departmentPrograms.forEach(program => {
                    programSelect.innerHTML += `<option value="${program.id}">${program.name}</option>`;
                });
            }
        }

        // Load programs by department for edit modal
        function loadEditProgramsByDepartment() {
            const departmentId = document.getElementById('editScheduleDepartment').value;
            const programSelect = document.getElementById('editScheduleProgram');
            
            programSelect.innerHTML = '<option value="">Select Program</option>';
            
            if (departmentId) {
                const departmentPrograms = programs.filter(p => p.departmentId == departmentId);
                departmentPrograms.forEach(program => {
                    programSelect.innerHTML += `<option value="${program.id}">${program.name}</option>`;
                });
            }
        }

        // Open Add Schedule Modal
async function openAddScheduleModal() {
    await populateDropdowns();
    resetForm('addScheduleForm');
    openModal('addScheduleModal');
}

        // Save new schedule
        function saveSchedule() {
            if (!validateForm('addScheduleForm')) return;

            const scheduleData = {
                programId: parseInt(document.getElementById('scheduleProgram').value),
                departmentId: parseInt(document.getElementById('scheduleDepartment').value),
                agencyId: parseInt(document.getElementById('scheduleAgency').value),
                levelId: parseInt(document.getElementById('scheduleLevel').value),
                cycleId: parseInt(document.getElementById('scheduleCycle').value),
                coveragePeriod: document.getElementById('scheduleCoveragePeriod').value,
                visitDate: document.getElementById('scheduleVisitDate').value,
                status: document.getElementById('scheduleStatus').value,
                priority: document.getElementById('schedulePriority').value || 'Normal',
                notes: document.getElementById('scheduleNotes').value || ''
            };

            const result = createScheduleWithTracking(scheduleData);
            if (result) {
                loadAllSchedules();
                closeModal('addScheduleModal');
                showToast('Schedule created successfully!');
                
                // Reload any schedule displays if they exist
                if (typeof loadUpcomingDeadlines === 'function') {
                    loadUpcomingDeadlines();
                }
            }
        }

        // View schedule details
        function viewSchedule(id) {
            const schedule = getEntityById(schedules, id);
            if (!schedule) return;

            const program = getEntityById(programs, schedule.programId);
            const department = getEntityById(departments, schedule.departmentId);
            const agency = getEntityById(agencies, schedule.agencyId);
            const level = getEntityById(levels, schedule.levelId);
            const cycle = getEntityById(cycles, schedule.cycleId);

            document.getElementById('viewDepartment').textContent = department ? department.name : 'N/A';
            document.getElementById('viewProgram').textContent = program ? program.name : 'N/A';
            document.getElementById('viewAgency').textContent = agency ? agency.name : 'N/A';
            document.getElementById('viewLevel').textContent = level ? level.level : 'N/A';
            document.getElementById('viewCycle').textContent = cycle ? cycle.type : 'N/A';
            document.getElementById('viewCoveragePeriod').textContent = schedule.coveragePeriod;
            document.getElementById('viewVisitDate').textContent = formatDate(schedule.visitDate);
            document.getElementById('viewStatus').innerHTML = getStatusBadge(schedule.status);
            document.getElementById('viewPriority').textContent = schedule.priority || 'Normal';
            document.getElementById('viewNotes').textContent = schedule.notes || 'No notes';

            openModal('viewScheduleModal');
        }

        // Edit schedule
        function editSchedule(id) {
            const schedule = getEntityById(schedules, id);
            if (!schedule) return;

            currentEditingScheduleId = id;

            // Populate dropdowns
            populateDropdowns();

            // Populate edit form
            document.getElementById('editScheduleDepartment').value = schedule.departmentId;
            loadEditProgramsByDepartment();
            setTimeout(() => {
                document.getElementById('editScheduleProgram').value = schedule.programId;
            }, 100);
            
            document.getElementById('editScheduleAgency').value = schedule.agencyId;
            document.getElementById('editScheduleLevel').value = schedule.levelId;
            document.getElementById('editScheduleCycle').value = schedule.cycleId;
            document.getElementById('editScheduleCoveragePeriod').value = schedule.coveragePeriod;
            document.getElementById('editScheduleVisitDate').value = schedule.visitDate;
            document.getElementById('editScheduleStatus').value = schedule.status;
            document.getElementById('editSchedulePriority').value = schedule.priority || 'Normal';
            document.getElementById('editScheduleNotes').value = schedule.notes || '';

            openModal('editScheduleModal');
        }

        // Update schedule
        function updateSchedule() {
            if (!validateForm('editScheduleForm')) return;

            const updateData = {
                programId: parseInt(document.getElementById('editScheduleProgram').value),
                departmentId: parseInt(document.getElementById('editScheduleDepartment').value),
                agencyId: parseInt(document.getElementById('editScheduleAgency').value),
                levelId: parseInt(document.getElementById('editScheduleLevel').value),
                cycleId: parseInt(document.getElementById('editScheduleCycle').value),
                coveragePeriod: document.getElementById('editScheduleCoveragePeriod').value,
                visitDate: document.getElementById('editScheduleVisitDate').value,
                status: document.getElementById('editScheduleStatus').value,
                priority: document.getElementById('editSchedulePriority').value || 'Normal',
                notes: document.getElementById('editScheduleNotes').value || ''
            };

            const result = updateScheduleWithTracking(currentEditingScheduleId, updateData);
            if (result) {
                loadAllSchedules();
                closeModal('editScheduleModal');
                showToast('Schedule updated successfully!');
                
                // Reload any schedule displays if they exist
                if (typeof loadUpcomingDeadlines === 'function') {
                    loadUpcomingDeadlines();
                }
            }
        }

        // Delete schedule
        function deleteSchedule(id) {
            if (confirm('Are you sure you want to delete this schedule?')) {
                const result = deleteScheduleWithTracking(id);
                if (result) {
                    loadAllSchedules();
                    showToast('Schedule deleted successfully!');
                    
                    // Reload any schedule displays if they exist
                    if (typeof loadUpcomingDeadlines === 'function') {
                        loadUpcomingDeadlines();
                    }
                }
            }
        }

        // Get status badge
        function getStatusBadge(status) {
            const statusMap = {
                'Preparation': 'bg-info',
                'In Progress': 'bg-warning',
                'Completed': 'bg-success',
                'Postponed': 'bg-danger'
            };
            return `<span class="badge ${statusMap[status] || 'bg-secondary'}">${status}</span>`;
        }

        // Set current month in calendar input
        function setCurrentMonth() {
            const now = new Date();
            const monthString = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
            document.getElementById('calendarMonth').value = monthString;
        }

        // Load visit calendar
        function loadVisitCalendar() {
            const viewType = document.getElementById('calendarView').value;
            
            if (viewType === 'list') {
                showListView();
            } else {
                showCalendarView();
            }
        }

        // Show calendar grid view
        function showCalendarView() {
            document.getElementById('calendarContainer').style.display = 'block';
            document.getElementById('calendarListView').style.display = 'none';
            
            const grid = document.getElementById('calendarGrid');
            grid.innerHTML = `
                <div class="col-12">
                    <div class="calendar-month-view">
                        <div class="text-center mb-3">
                            <h5>${getCurrentMonthName()} ${currentCalendarDate.getFullYear()}</h5>
                        </div>
                        ${generateCalendarGrid()}
                    </div>
                </div>
            `;
        }

        // Show list view
        function showListView() {
            document.getElementById('calendarContainer').style.display = 'none';
            document.getElementById('calendarListView').style.display = 'block';
            
            const tbody = document.getElementById('calendarListBody');
            tbody.innerHTML = '';
            
            visitCalendarData.forEach(visit => {
                const program = getEntityById(programs, visit.programId);
                const row = `
                    <tr>
                        <td>${formatDate(visit.date)}</td>
                        <td><strong>${program ? program.name : 'Unknown'}</strong></td>
                        <td>${visit.agency}</td>
                        <td>${visit.visitType}</td>
                        <td>
                            <span class="badge ${getVisitStatusClass(visit.status)}">${visit.status}</span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editVisit(${visit.id})">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteVisit(${visit.id})">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Generate calendar grid
        function generateCalendarGrid() {
            const year = currentCalendarDate.getFullYear();
            const month = currentCalendarDate.getMonth();
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());
            
            let calendarHtml = '<div class="calendar-grid"><div class="row">';
            
            // Day headers
            const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            dayHeaders.forEach(day => {
                calendarHtml += `<div class="col calendar-day-header">${day}</div>`;
            });
            calendarHtml += '</div>';
            
            // Calendar days
            let currentDate = new Date(startDate);
            for (let week = 0; week < 6; week++) {
                calendarHtml += '<div class="row">';
                for (let day = 0; day < 7; day++) {
                    const isCurrentMonth = currentDate.getMonth() === month;
                    const dateString = currentDate.toISOString().split('T')[0];
                    const dayVisits = visitCalendarData.filter(v => v.date === dateString);
                    
                    calendarHtml += `
                        <div class="col calendar-day ${isCurrentMonth ? '' : 'other-month'}">
                            <div class="day-number">${currentDate.getDate()}</div>
                            ${dayVisits.map(visit => {
                                const program = getEntityById(programs, visit.programId);
                                return `<div class="visit-item" title="${visit.visitType} - ${program?.name}">${visit.agency}</div>`;
                            }).join('')}
                        </div>
                    `;
                    currentDate.setDate(currentDate.getDate() + 1);
                }
                calendarHtml += '</div>';
                
                if (currentDate > lastDay && currentDate.getDate() > 7) break;
            }
            
            calendarHtml += '</div>';
            return calendarHtml;
        }

        // Load deadlines
        function loadDeadlines() {
            const container = document.getElementById('deadlinesContainer');
            container.innerHTML = '';
            
            // Group deadlines by status
            const groupedDeadlines = {
                'Overdue': deadlinesData.filter(d => new Date(d.dueDate) < new Date() && d.status !== 'Completed'),
                'Upcoming': deadlinesData.filter(d => {
                    const dueDate = new Date(d.dueDate);
                    const today = new Date();
                    const thirtyDaysFromNow = new Date();
                    thirtyDaysFromNow.setDate(today.getDate() + 30);
                    return dueDate >= today && dueDate <= thirtyDaysFromNow && d.status !== 'Completed';
                }),
                'Future': deadlinesData.filter(d => {
                    const dueDate = new Date(d.dueDate);
                    const thirtyDaysFromNow = new Date();
                    thirtyDaysFromNow.setDate(new Date().getDate() + 30);
                    return dueDate > thirtyDaysFromNow && d.status !== 'Completed';
                })
            };
            
            Object.keys(groupedDeadlines).forEach(category => {
                const deadlines = groupedDeadlines[category];
                if (deadlines.length > 0) {
                    const card = `
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header ${getDeadlineCategoryClass(category)} text-white">
                                    <h6 class="card-title mb-0">${category} (${deadlines.length})</h6>
                                </div>
                                <div class="card-body">
                                    ${deadlines.map(deadline => {
                                        const program = getEntityById(programs, deadline.programId);
                                        const daysUntil = Math.ceil((new Date(deadline.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
                                        return `
                                            <div class="deadline-item border-bottom py-2">
                                                <strong>${deadline.title}</strong>
                                                <span class="badge ${getPriorityClass(deadline.priority)} ms-2">${deadline.priority}</span><br>
                                                <small class="text-muted">Due: ${formatDate(deadline.dueDate)}</small><br>
                                                <small class="text-muted">Program: ${program?.name || 'All Programs'}</small><br>
                                                <small class="text-muted">${daysUntil > 0 ? `${daysUntil} days remaining` : `${Math.abs(daysUntil)} days overdue`}</small>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        </div>
                    `;
                    container.innerHTML += card;
                }
            });
        }

        // Load evaluation periods
        function loadEvaluationPeriods() {
            const tbody = document.getElementById('evaluationPeriodsTableBody');
            tbody.innerHTML = '';
            
            evaluationPeriodsData.forEach(period => {
                const program = getEntityById(programs, period.programId);
                const startDate = new Date(period.startDate);
                const endDate = new Date(period.endDate);
                const duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
                
                const row = `
                    <tr>
                        <td>${period.id}</td>
                        <td><strong>${period.name}</strong></td>
                        <td>${program ? program.name : 'Unknown'}</td>
                        <td>${formatDate(period.startDate)}</td>
                        <td>${formatDate(period.endDate)}</td>
                        <td>${duration} days</td>
                        <td>
                            <span class="badge ${getEvaluationStatusClass(period.status)}">${period.status}</span>
                        </td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${getProgressBarClass(period.progress)}" 
                                     style="width: ${period.progress}%" 
                                     title="${period.progress}% Complete">
                                    ${period.progress}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editEvaluationPeriod(${period.id})">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteEvaluationPeriod(${period.id})">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Modal functions
        function openAddVisitModal() {
            populateSelect('visitProgram', programs, 'name');
            openModal('addVisitModal');
        }

        function openAddDeadlineModal() {
            populateSelect('deadlineProgram', programs, 'name');
            openModal('addDeadlineModal');
        }

        function openAddEvaluationPeriodModal() {
            populateSelect('evaluationPeriodProgram', programs, 'name');
            openModal('addEvaluationPeriodModal');
        }

        // Add functions
        function addVisit() {
            if (!validateForm('addVisitForm')) return;
            
            const newVisit = {
                id: generateId(visitCalendarData),
                programId: parseInt(document.getElementById('visitProgram').value),
                agency: document.getElementById('visitAgency').value,
                visitType: document.getElementById('visitType').value,
                date: document.getElementById('visitDate').value,
                startTime: document.getElementById('visitStartTime').value,
                endTime: document.getElementById('visitEndTime').value,
                status: 'Scheduled',
                notes: document.getElementById('visitNotes').value
            };
            
            visitCalendarData.push(newVisit);
            loadVisitCalendar();
            closeModal('addVisitModal');
            showToast('Visit scheduled successfully!');
        }

        function addDeadline() {
            if (!validateForm('addDeadlineForm')) return;
            
            const newDeadline = {
                id: generateId(deadlinesData),
                title: document.getElementById('deadlineTitle').value,
                type: document.getElementById('deadlineType').value,
                dueDate: document.getElementById('deadlineDate').value,
                priority: document.getElementById('deadlinePriority').value,
                programId: parseInt(document.getElementById('deadlineProgram').value) || null,
                description: document.getElementById('deadlineDescription').value,
                status: 'Pending'
            };
            
            deadlinesData.push(newDeadline);
            loadDeadlines();
            closeModal('addDeadlineModal');
            showToast('Deadline added successfully!');
        }

        function addEvaluationPeriod() {
            if (!validateForm('addEvaluationPeriodForm')) return;
            
            const newPeriod = {
                id: generateId(evaluationPeriodsData),
                name: document.getElementById('evaluationPeriodName').value,
                programId: parseInt(document.getElementById('evaluationPeriodProgram').value),
                startDate: document.getElementById('evaluationPeriodStartDate').value,
                endDate: document.getElementById('evaluationPeriodEndDate').value,
                status: document.getElementById('evaluationPeriodStatus').value,
                progress: 0,
                description: document.getElementById('evaluationPeriodDescription').value
            };
            
            evaluationPeriodsData.push(newPeriod);
            loadEvaluationPeriods();
            closeModal('addEvaluationPeriodModal');
            showToast('Evaluation period added successfully!');
        }

        // View change functions
        function changeCalendarView() {
            loadVisitCalendar();
        }

        function changeCalendarMonth() {
            const monthValue = document.getElementById('calendarMonth').value;
            const [year, month] = monthValue.split('-');
            currentCalendarDate = new Date(year, month - 1, 1);
            loadVisitCalendar();
        }

        // Helper functions
        function getCurrentMonthName() {
            const months = ['January', 'February', 'March', 'April', 'May', 'June',
                          'July', 'August', 'September', 'October', 'November', 'December'];
            return months[currentCalendarDate.getMonth()];
        }

        function getVisitStatusClass(status) {
            switch(status) {
                case 'Scheduled': return 'bg-success';
                case 'In Progress': return 'bg-warning';
                case 'Completed': return 'bg-info';
                case 'Cancelled': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }

        function getDeadlineCategoryClass(category) {
            switch(category) {
                case 'Overdue': return 'bg-danger';
                case 'Upcoming': return 'bg-warning';
                case 'Future': return 'bg-success';
                default: return 'bg-secondary';
            }
        }

        function getPriorityClass(priority) {
            switch(priority) {
                case 'Critical': return 'bg-danger';
                case 'High': return 'bg-warning';
                case 'Medium': return 'bg-info';
                case 'Low': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function getEvaluationStatusClass(status) {
            switch(status) {
                case 'Active': return 'bg-success';
                case 'Planned': return 'bg-info';
                case 'Completed': return 'bg-primary';
                case 'Postponed': return 'bg-warning';
                default: return 'bg-secondary';
            }
        }

        function getProgressBarClass(progress) {
            if (progress >= 80) return 'bg-success';
            if (progress >= 60) return 'bg-info';
            if (progress >= 40) return 'bg-warning';
            return 'bg-danger';
        }

        function populateSelect(selectId, data, textField) {
            const select = document.getElementById(selectId);
            const currentValue = select.value;
            select.innerHTML = '<option value="">Select...</option>';
            
            data.forEach(item => {
                select.innerHTML += `<option value="${item.id}">${item[textField]}</option>`;
            });
            
            if (currentValue) select.value = currentValue;
        }

        // Placeholder functions for edit and delete operations
        function editVisit(id) { showToast('Edit visit functionality - to be implemented'); }
        function deleteVisit(id) { 
            if (confirm('Are you sure you want to delete this visit?')) {
                visitCalendarData = visitCalendarData.filter(v => v.id !== id);
                loadVisitCalendar();
                showToast('Visit deleted successfully!');
            }
        }
        function editEvaluationPeriod(id) { showToast('Edit evaluation period functionality - to be implemented'); }
        function deleteEvaluationPeriod(id) {
            if (confirm('Are you sure you want to delete this evaluation period?')) {
                evaluationPeriodsData = evaluationPeriodsData.filter(p => p.id !== id);
                loadEvaluationPeriods();
                showToast('Evaluation period deleted successfully!');
            }
        }

        // Filter functions (placeholders)
        function filterCalendarByAgency() { loadVisitCalendar(); }
        function filterDeadlines() { loadDeadlines(); }
        function filterEvaluationPeriods() { loadEvaluationPeriods(); }
        function searchEvaluationPeriods() { 
            const searchTerm = document.getElementById('searchEvaluationPeriods').value.toLowerCase();
            const rows = document.querySelectorAll('#evaluationPeriodsTableBody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }

        // Load all schedules using fetch (AJAX)
        async function loadAllSchedules() {
            const tbody = document.getElementById('allSchedulesTableBody');
            tbody.innerHTML = '';
            try {
                const response = await fetch('schedules.json');
                if (!response.ok) throw new Error('Network response was not ok');
                const data = await response.json();
                // Override the global schedules array with fetched data
                window.schedules = data;
                data.forEach(schedule => {
                    const program = getEntityById(programs, schedule.programId);
                    const department = getEntityById(departments, schedule.departmentId);
                    const agency = getEntityById(agencies, schedule.agencyId);
                    const level = getEntityById(levels, schedule.levelId);
                    
                    const row = `
                        <tr>
                            <td><strong>${program ? program.name : 'N/A'}</strong></td>
                            <td>${department ? department.name : 'N/A'}</td>
                            <td>${agency ? agency.name : 'N/A'}</td>
                            <td>${level ? level.level : 'N/A'}</td>
                            <td>${schedule.coveragePeriod || ''}</td>
                            <td>${schedule.visitDate || ''}</td>
                            <td>${schedule.status || ''}</td>
                            <td>${schedule.priority || ''}</td>
                            <td>${schedule.notes || ''}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewSchedule(${schedule.id})">View</button>
                                <button class="btn btn-sm btn-outline-success" onclick="editSchedule(${schedule.id})">Update</button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteSchedule(${schedule.id})">Delete</button>
                            </td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });
            } catch (error) {
                console.error('Failed to load schedules:', error);
                tbody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">Error loading schedules.</td></tr>`;
            }

            // If no schedules were fetched, display a friendly message
            if (!window.schedules || window.schedules.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                <h5>No schedules found</h5>
                                <p>Click "Add Schedule" to create your first accreditation schedule.</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }
        }

        // Populate filters for schedules
        function populateScheduleFilters() {
            // Populate department filter
            const deptFilter = document.getElementById('scheduleDepartmentFilter');
            if (deptFilter) {
                deptFilter.innerHTML = '<option value="all">All Departments</option>';
                departments.forEach(dept => {
                    deptFilter.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
                });
            }
        }

        // Search schedules
        function searchSchedules() {
            const searchTerm = document.getElementById('searchSchedules').value.toLowerCase();
            const rows = document.querySelectorAll('#allSchedulesTableBody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }

        // Filter schedules
        function filterSchedules() {
            const statusFilter = document.getElementById('scheduleStatusFilter').value;
            const priorityFilter = document.getElementById('schedulePriorityFilter').value;
            const departmentFilter = document.getElementById('scheduleDepartmentFilter').value;
            const rows = document.querySelectorAll('#allSchedulesTableBody tr');

            rows.forEach(row => {
                const cells = row.getElementsByTagName('td');
                if (cells.length < 7) return; // Skip empty rows

                let showRow = true;

                // Status filter
                if (statusFilter !== 'all') {
                    const statusCell = cells[5]; // Status column
                    const statusText = statusCell.textContent.trim();
                    if (!statusText.includes(statusFilter)) {
                        showRow = false;
                    }
                }

                // Priority filter
                if (priorityFilter !== 'all') {
                    const priorityCell = cells[6]; // Priority column
                    const priorityText = priorityCell.textContent.trim();
                    if (!priorityText.includes(priorityFilter)) {
                        showRow = false;
                    }
                }

                // Department filter
                if (departmentFilter !== 'all') {
                    const departmentCell = cells[1]; // Department column
                    const schedule = schedules.find(s => {
                        const program = getEntityById(programs, s.programId);
                        const department = getEntityById(departments, s.departmentId);
                        return department && departmentCell.textContent.includes(department.name);
                    });
                    if (!schedule || schedule.departmentId != departmentFilter) {
                        showRow = false;
                    }
                }

                row.style.display = showRow ? '' : 'none';
            });
        }

        // Refresh schedules list
        function refreshSchedulesList() {
            loadAllSchedules();
            showToast('Schedules list refreshed successfully!');
        }

        // Get priority badge class
        function getPriorityBadgeClass(priority) {
            switch(priority) {
                case 'Urgent': return 'bg-danger';
                case 'High': return 'bg-warning';
                case 'Normal': return 'bg-success';
                default: return 'bg-secondary';
            }
        }
    </script>

    <style>
        .calendar-grid {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        
        .calendar-day-header {
            background-color: #f8f9fa;
            padding: 0.5rem;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid #dee2e6;
        }
        
        .calendar-day {
            min-height: 120px;
            padding: 0.25rem;
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            position: relative;
        }
        
        .calendar-day.other-month {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        
        .day-number {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .visit-item {
            background-color: #007bff;
            color: white;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-bottom: 0.125rem;
            cursor: pointer;
        }
        
        .deadline-item {
            margin-bottom: 0.5rem;
        }
        
        .deadline-item:last-child {
            border-bottom: none !important;
            margin-bottom: 0;
        }
    </style>
</body>
</html> 