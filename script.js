// ADAMS - Accreditation Document Administration & Management System
// Global Variables and Data Storage

// Sidebar toggle functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (sidebar && overlay) {
        sidebar.classList.toggle('show');
        overlay.classList.toggle('show');
    }
}

// Global variables
let currentUser = null;
let systemUsers = {};
let globalData = {
    programs: [],
    documents: [],
    cycles: [],
    notifications: [],
    reports: [],
    standards: []
};

// Sample data arrays for the system
let departments = [
    { id: 1, name: 'College of Information Technology and Engineering (CITE)', code: 'CIT<PERSON>', dean: 'Dr. <PERSON>', programs: 8, status: 'Active', established: '2010' },
    { id: 2, name: 'College of Business Administration (CBA)', code: 'CBA', dean: 'Dr. <PERSON>', programs: 6, status: 'Active', established: '2008' },
    { id: 3, name: 'College of Education (COE)', code: 'COE', dean: 'Dr. <PERSON>', programs: 5, status: 'Active', established: '2005' },
    { id: 4, name: 'College of Nursing (CON)', code: 'CON', dean: 'Dr. <PERSON>', programs: 3, status: 'Active', established: '2012' },
    { id: 5, name: 'College of Liberal Arts (CLA)', code: 'CLA', dean: 'Dr. Robert Johnson', programs: 4, status: 'Active', established: '2007' }
];

let programs = [
    { id: 1, name: 'Bachelor of Science in Computer Science', code: 'BSCS', departmentId: 1, level: 'Undergraduate', status: 'Active', students: 245, faculty: 15 },
    { id: 2, name: 'Bachelor of Science in Information Technology', code: 'BSIT', departmentId: 1, level: 'Undergraduate', status: 'Active', students: 189, faculty: 12 },
    { id: 3, name: 'Bachelor of Science in Computer Engineering', code: 'BSCpE', departmentId: 1, level: 'Undergraduate', status: 'Active', students: 156, faculty: 10 },
    { id: 4, name: 'Bachelor of Science in Business Administration', code: 'BSBA', departmentId: 2, level: 'Undergraduate', status: 'Active', students: 298, faculty: 18 },
    { id: 5, name: 'Bachelor of Science in Accountancy', code: 'BSA', departmentId: 2, level: 'Undergraduate', status: 'Active', students: 234, faculty: 14 },
    { id: 6, name: 'Bachelor of Science in Marketing', code: 'BSM', departmentId: 2, level: 'Undergraduate', status: 'Active', students: 167, faculty: 11 },
    { id: 7, name: 'Bachelor of Elementary Education', code: 'BEEd', departmentId: 3, level: 'Undergraduate', status: 'Active', students: 178, faculty: 13 },
    { id: 8, name: 'Bachelor of Secondary Education', code: 'BSEd', departmentId: 3, level: 'Undergraduate', status: 'Active', students: 145, faculty: 12 },
    { id: 9, name: 'Bachelor of Science in Nursing', code: 'BSN', departmentId: 4, level: 'Undergraduate', status: 'Active', students: 267, faculty: 16 },
    { id: 10, name: 'Bachelor of Arts in English', code: 'BAE', departmentId: 5, level: 'Undergraduate', status: 'Active', students: 89, faculty: 8 }
];

let agencies = [
    { id: 1, name: 'Philippine Association of Colleges and Universities Commission on Accreditation', code: 'PACUCOA', type: 'National', website: 'www.pacucoa.ph', established: '1957' },
    { id: 2, name: 'Association of Christian Universities and Colleges in Asia', code: 'ACUCA', type: 'Regional', website: 'www.acuca.net', established: '1963' },
    { id: 3, name: 'Philippine Accrediting Association of Schools, Colleges and Universities', code: 'PAASCU', type: 'National', website: 'www.paascu.org.ph', established: '1957' },
    { id: 4, name: 'Association of Accrediting Agencies of the Philippines', code: 'AAAP', type: 'National', website: 'www.aaap.org.ph', established: '1977' },
    { id: 5, name: 'Accrediting Agency of Chartered Colleges and Universities in the Philippines', code: 'AACCUP', type: 'National', website: 'www.aaccup.org.ph', established: '1973' }
];

let levels = [
    { id: 1, level: 'Candidate', description: 'Initial stage seeking formal recognition', requirements: 'Basic compliance with standards', duration: '2-3 years' },
    { id: 2, level: 'Level I', description: 'Formal recognition - basic accreditation', requirements: 'Meets minimum standards', duration: '3-5 years' },
    { id: 3, level: 'Level II', description: 'Full accreditation with quality assurance', requirements: 'Demonstrates quality improvement', duration: '5 years' },
    { id: 4, level: 'Level III', description: 'Accredited with distinction', requirements: 'Excellence in all areas', duration: '5-7 years' },
    { id: 5, level: 'Level IV', description: 'Institutional accreditation', requirements: 'Autonomous status capability', duration: '7-10 years' }
];

let cycles = [
    { id: 1, type: 'Initial Accreditation', description: 'First-time accreditation process', duration: '18-24 months', phases: 4 },
    { id: 2, type: 'Re-accreditation', description: 'Renewal of existing accreditation', duration: '12-18 months', phases: 3 },
    { id: 3, type: 'Level Advancement', description: 'Moving to higher accreditation level', duration: '12-15 months', phases: 3 },
    { id: 4, type: 'Follow-up Visit', description: 'Addressing specific recommendations', duration: '6-9 months', phases: 2 },
    { id: 5, type: 'Special Visit', description: 'Investigating specific concerns', duration: '3-6 months', phases: 2 }
];

let schedules = [
    { 
        id: 1, 
        programId: 1, 
        departmentId: 1, 
        agencyId: 3, 
        levelId: 2, 
        cycleId: 2, 
        coveragePeriod: 'AY 2025-2027', 
        visitDate: '2025-03-15', 
        status: 'Preparation', 
        priority: 'High',
        notes: 'Re-accreditation for BSCS program. Self-study report due January 2025.',
        createdBy: 'admin1',
        createdDate: '2024-12-15',
        lastModifiedBy: 'admin1',
        lastModifiedDate: '2024-12-15'
    },
    { 
        id: 2, 
        programId: 2, 
        departmentId: 1, 
        agencyId: 5, 
        levelId: 1, 
        cycleId: 1, 
        coveragePeriod: 'AY 2025-2028', 
        visitDate: '2025-01-20', 
        status: 'In Progress', 
        priority: 'Urgent',
        notes: 'Initial accreditation for BSIT program. Site visit scheduled.',
        createdBy: 'chair1',
        createdDate: '2024-12-10',
        lastModifiedBy: 'admin1',
        lastModifiedDate: '2024-12-14'
    },
    { 
        id: 3, 
        programId: 4, 
        departmentId: 2, 
        agencyId: 1, 
        levelId: 3, 
        cycleId: 3, 
        coveragePeriod: 'AY 2025-2030', 
        visitDate: '2025-06-10', 
        status: 'Preparation', 
        priority: 'Normal',
        notes: 'Level advancement for BSBA program from Level II to Level III.',
        createdBy: 'chair2',
        createdDate: '2024-12-12',
        lastModifiedBy: 'chair2',
        lastModifiedDate: '2024-12-12'
    },
    { 
        id: 4, 
        programId: 9, 
        departmentId: 4, 
        agencyId: 3, 
        levelId: 2, 
        cycleId: 2, 
        coveragePeriod: 'AY 2024-2027', 
        visitDate: '2024-12-28', 
        status: 'Completed', 
        priority: 'Normal',
        notes: 'Re-accreditation completed successfully. Level II maintained.',
        createdBy: 'admin1',
        createdDate: '2024-10-15',
        lastModifiedBy: 'admin1',
        lastModifiedDate: '2024-12-28'
    },
    { 
        id: 5, 
        programId: 7, 
        departmentId: 3, 
        agencyId: 2, 
        levelId: 1, 
        cycleId: 4, 
        coveragePeriod: 'AY 2025-2026', 
        visitDate: '2025-04-22', 
        status: 'Preparation', 
        priority: 'High',
        notes: 'Follow-up visit to address previous recommendations for BEEd program.',
        createdBy: 'chair3',
        createdDate: '2024-12-08',
        lastModifiedBy: 'chair3',
        lastModifiedDate: '2024-12-13'
    },
    { 
        id: 6, 
        programId: 5, 
        departmentId: 2, 
        agencyId: 4, 
        levelId: 2, 
        cycleId: 2, 
        coveragePeriod: 'AY 2025-2028', 
        visitDate: '2025-08-15', 
        status: 'Preparation', 
        priority: 'Normal',
        notes: 'Re-accreditation for BSA program. Document preparation in progress.',
        createdBy: 'chair2',
        createdDate: '2024-12-05',
        lastModifiedBy: 'subchair1',
        lastModifiedDate: '2024-12-14'
    },
    { 
        id: 7, 
        programId: 3, 
        departmentId: 1, 
        agencyId: 5, 
        levelId: 1, 
        cycleId: 1, 
        coveragePeriod: 'AY 2025-2028', 
        visitDate: '2025-09-30', 
        status: 'Preparation', 
        priority: 'Normal',
        notes: 'Initial accreditation for BSCpE program. Faculty qualification review needed.',
        createdBy: 'chair1',
        createdDate: '2024-11-20',
        lastModifiedBy: 'member1',
        lastModifiedDate: '2024-12-10'
    },
    { 
        id: 8, 
        programId: 10, 
        departmentId: 5, 
        agencyId: 1, 
        levelId: 1, 
        cycleId: 3, 
        coveragePeriod: 'AY 2026-2029', 
        visitDate: '2026-02-14', 
        status: 'Preparation', 
        priority: 'Low',
        notes: 'Level advancement preparation for BAE program.',
        createdBy: 'chair4',
        createdDate: '2024-12-01',
        lastModifiedBy: 'chair4',
        lastModifiedDate: '2024-12-01'
    }
];

// Documents data with role tracking
let documents = [
    { 
        id: 1, 
        title: 'BSCS Self-Study Report 2025', 
        type: 'Self-Study Report', 
        programId: 1, 
        status: 'In Review',
        uploadedBy: 'member1',
        uploadedDate: '2024-12-10',
        reviewedBy: 'chair1',
        reviewDate: '2024-12-12',
        approvedBy: null,
        approvalDate: null,
        area: 1,
        version: '1.2'
    },
    { 
        id: 2, 
        title: 'Faculty Qualification Matrix', 
        type: 'Faculty Documents', 
        programId: 2, 
        status: 'Approved',
        uploadedBy: 'member2',
        uploadedDate: '2024-12-08',
        reviewedBy: 'subchair1',
        reviewDate: '2024-12-09',
        approvedBy: 'chair1',
        approvalDate: '2024-12-10',
        area: 2,
        version: '2.0'
    },
    { 
        id: 3, 
        title: 'Strategic Plan 2025-2030', 
        type: 'Strategic Document', 
        programId: 4, 
        status: 'Pending Review',
        uploadedBy: 'chair2',
        uploadedDate: '2024-12-13',
        reviewedBy: null,
        reviewDate: null,
        approvedBy: null,
        approvalDate: null,
        area: 3,
        version: '1.0'
    }
];

// Activity log for tracking changes across roles
let activityLog = [
    { 
        id: 1, 
        action: 'Schedule Created', 
        description: 'Created BSIT Initial Accreditation schedule', 
        userId: 'chair1', 
        userName: 'Area Chair 1', 
        timestamp: '2024-12-10T09:30:00Z',
        entityType: 'schedule',
        entityId: 2
    },
    { 
        id: 2, 
        action: 'Document Uploaded', 
        description: 'Uploaded Faculty Qualification Matrix', 
        userId: 'member2', 
        userName: 'Faculty Member 2', 
        timestamp: '2024-12-08T14:15:00Z',
        entityType: 'document',
        entityId: 2
    },
    { 
        id: 3, 
        action: 'Schedule Updated', 
        description: 'Updated BSA Re-accreditation schedule notes', 
        userId: 'subchair1', 
        userName: 'Sub-Area Chair 1', 
        timestamp: '2024-12-14T11:20:00Z',
        entityType: 'schedule',
        entityId: 6
    },
    { 
        id: 4, 
        action: 'Document Approved', 
        description: 'Approved Faculty Qualification Matrix', 
        userId: 'chair1', 
        userName: 'Area Chair 1', 
        timestamp: '2024-12-10T16:45:00Z',
        entityType: 'document',
        entityId: 2
    }
];

// Centralized Data Management Functions

// Save all data to localStorage with proper structure
function saveAllDataToStorage() {
    const centralData = {
        departments,
        programs,
        agencies,
        levels,
        cycles,
        schedules,
        documents,
        activityLog,
        lastSync: new Date().toISOString()
    };
    localStorage.setItem('adamsSystemData', JSON.stringify(centralData));
}

// Load all data from localStorage
function loadAllDataFromStorage() {
    const stored = localStorage.getItem('adamsSystemData');
    if (stored) {
        const centralData = JSON.parse(stored);
        if (centralData.departments) departments = centralData.departments;
        if (centralData.programs) programs = centralData.programs;
        if (centralData.agencies) agencies = centralData.agencies;
        if (centralData.levels) levels = centralData.levels;
        if (centralData.cycles) cycles = centralData.cycles;
        if (centralData.schedules) schedules = centralData.schedules;
        if (centralData.documents) documents = centralData.documents;
        if (centralData.activityLog) activityLog = centralData.activityLog;
    }
}

// Add activity log entry
function addActivityLog(action, description, entityType = null, entityId = null) {
    const user = getCurrentUser();
    if (!user) return;

    const logEntry = {
        id: generateId(activityLog),
        action,
        description,
        userId: user.username || 'system',
        userName: user.name || 'System',
        timestamp: new Date().toISOString(),
        entityType,
        entityId
    };
    
    activityLog.unshift(logEntry); // Add to beginning for newest first
    
    // Keep only last 100 entries to prevent storage bloat
    if (activityLog.length > 100) {
        activityLog = activityLog.slice(0, 100);
    }
    
    saveAllDataToStorage();
}

// Get user's accessible data based on role and areas
function getUserAccessibleData(user, dataType) {
    if (!user) return [];
    
    switch (user.role) {
        case 'superadmin':
        case 'admin':
            // Full access to all data
            switch (dataType) {
                case 'schedules': return schedules;
                case 'documents': return documents;
                case 'programs': return programs;
                case 'departments': return departments;
                case 'activityLog': return activityLog;
                default: return [];
            }
            
        case 'areachair':
            // Access to data for their assigned areas
            switch (dataType) {
                case 'schedules': return schedules.filter(s => {
                    const program = getEntityById(programs, s.programId);
                    return program && user.areas.includes(program.departmentId);
                });
                case 'documents': return documents.filter(d => 
                    user.areas.includes(d.area) || d.uploadedBy === user.username || d.reviewedBy === user.username
                );
                case 'programs': return programs.filter(p => user.areas.includes(p.departmentId));
                case 'departments': return departments.filter(d => user.areas.includes(d.id));
                case 'activityLog': return activityLog.filter(log => {
                    // Show activities related to their areas or their own activities
                    return log.userId === user.username || 
                           (log.entityType === 'schedule' && getUserAccessibleData(user, 'schedules').some(s => s.id === log.entityId)) ||
                           (log.entityType === 'document' && getUserAccessibleData(user, 'documents').some(d => d.id === log.entityId));
                });
                default: return [];
            }
            
        case 'subareachair':
            // Access to data for their specific area
            switch (dataType) {
                case 'schedules': return schedules.filter(s => {
                    const program = getEntityById(programs, s.programId);
                    return program && user.areas.includes(program.departmentId);
                });
                case 'documents': return documents.filter(d => 
                    user.areas.includes(d.area) || d.uploadedBy === user.username || d.reviewedBy === user.username
                );
                case 'programs': return programs.filter(p => user.areas.includes(p.departmentId));
                case 'departments': return departments.filter(d => user.areas.includes(d.id));
                case 'activityLog': return activityLog.filter(log => 
                    log.userId === user.username ||
                    (log.entityType === 'document' && getUserAccessibleData(user, 'documents').some(d => d.id === log.entityId))
                );
                default: return [];
            }
            
        case 'member':
            // Limited access - mainly their own documents and related schedules
            switch (dataType) {
                case 'documents': return documents.filter(d => d.uploadedBy === user.username);
                case 'schedules': return schedules; // Can view all schedules for reference
                case 'programs': return programs; // Can view all programs for reference
                case 'departments': return departments; // Can view all departments for reference
                case 'activityLog': return activityLog.filter(log => log.userId === user.username);
                default: return [];
            }
            
        default:
            return [];
    }
}

// Enhanced CRUD operations with role tracking and data sync

// Create schedule with role tracking
function createScheduleWithTracking(scheduleData) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const newSchedule = {
        ...scheduleData,
        id: generateId(schedules),
        createdBy: user.username || 'system',
        createdDate: new Date().toISOString(),
        lastModifiedBy: user.username || 'system',
        lastModifiedDate: new Date().toISOString()
    };
    
    schedules.push(newSchedule);
    addActivityLog('Schedule Created', `Created schedule for ${getEntityById(programs, newSchedule.programId)?.name || 'Unknown Program'}`, 'schedule', newSchedule.id);
    saveAllDataToStorage();
    
    return newSchedule;
}

// Update schedule with role tracking
function updateScheduleWithTracking(scheduleId, updateData) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const scheduleIndex = schedules.findIndex(s => s.id === scheduleId);
    if (scheduleIndex === -1) return false;
    
    const oldSchedule = { ...schedules[scheduleIndex] };
    schedules[scheduleIndex] = {
        ...schedules[scheduleIndex],
        ...updateData,
        lastModifiedBy: user.username || 'system',
        lastModifiedDate: new Date().toISOString()
    };
    
    addActivityLog('Schedule Updated', `Updated schedule for ${getEntityById(programs, schedules[scheduleIndex].programId)?.name || 'Unknown Program'}`, 'schedule', scheduleId);
    saveAllDataToStorage();
    
    return schedules[scheduleIndex];
}

// Delete schedule with role tracking
function deleteScheduleWithTracking(scheduleId) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const schedule = getEntityById(schedules, scheduleId);
    if (!schedule) return false;
    
    schedules = schedules.filter(s => s.id !== scheduleId);
    addActivityLog('Schedule Deleted', `Deleted schedule for ${getEntityById(programs, schedule.programId)?.name || 'Unknown Program'}`, 'schedule', scheduleId);
    saveAllDataToStorage();
    
    return true;
}

// Create document with role tracking
function createDocumentWithTracking(documentData) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const newDocument = {
        ...documentData,
        id: generateId(documents),
        uploadedBy: user.username || 'system',
        uploadedDate: new Date().toISOString(),
        status: 'Pending Review'
    };
    
    documents.push(newDocument);
    addActivityLog('Document Uploaded', `Uploaded ${newDocument.title}`, 'document', newDocument.id);
    saveAllDataToStorage();
    
    return newDocument;
}

// Update document with role tracking
function updateDocumentWithTracking(documentId, updateData) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const documentIndex = documents.findIndex(d => d.id === documentId);
    if (documentIndex === -1) return false;
    
    const oldStatus = documents[documentIndex].status;
    documents[documentIndex] = {
        ...documents[documentIndex],
        ...updateData
    };
    
    // Add specific tracking for status changes
    if (updateData.status && updateData.status !== oldStatus) {
        if (updateData.status === 'In Review') {
            documents[documentIndex].reviewedBy = user.username;
            documents[documentIndex].reviewDate = new Date().toISOString();
            addActivityLog('Document Review Started', `Started reviewing ${documents[documentIndex].title}`, 'document', documentId);
        } else if (updateData.status === 'Approved') {
            documents[documentIndex].approvedBy = user.username;
            documents[documentIndex].approvalDate = new Date().toISOString();
            addActivityLog('Document Approved', `Approved ${documents[documentIndex].title}`, 'document', documentId);
        } else if (updateData.status === 'Rejected') {
            addActivityLog('Document Rejected', `Rejected ${documents[documentIndex].title}`, 'document', documentId);
        }
    }
    
    saveAllDataToStorage();
    return documents[documentIndex];
}

// Get recent activity for dashboard
function getRecentActivity(limit = 10) {
    const user = getCurrentUser();
    if (!user) return [];
    
    return getUserAccessibleData(user, 'activityLog').slice(0, limit);
}

// Initialize system data in localStorage if not exists
function initializeSystemData() {
    if (!localStorage.getItem('adamsSystemData')) {
        localStorage.setItem('adamsSystemData', JSON.stringify(globalData));
    }
    if (!localStorage.getItem('adamsSystemUsers')) {
        const defaultUsers = {
            'admin1': { password: 'hazel', role: 'superadmin', name: 'Super Administrator', areas: 'all', status: 'active', email: '<EMAIL>', lastLogin: null },
            'admin': { password: 'admin', role: 'admin', name: 'System Administrator', areas: 'all', status: 'active', email: '<EMAIL>', lastLogin: null },
            'chair1': { password: 'admin', role: 'areachair', name: 'Area Chair 1', areas: [1, 2], status: 'active', email: '<EMAIL>', lastLogin: null },
            'chair2': { password: 'admin', role: 'areachair', name: 'Area Chair 2', areas: [3, 4], status: 'active', email: '<EMAIL>', lastLogin: null },
            'chair3': { password: 'admin', role: 'areachair', name: 'Area Chair 3', areas: [5, 6], status: 'active', email: '<EMAIL>', lastLogin: null },
            'chair4': { password: 'admin', role: 'areachair', name: 'Area Chair 4', areas: [7, 8], status: 'active', email: '<EMAIL>', lastLogin: null },
            'subchair1': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 1', areas: [1], status: 'active', email: '<EMAIL>', lastLogin: null },
            'subchair2': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 2', areas: [2], status: 'active', email: '<EMAIL>', lastLogin: null },
            'subchair3': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 3', areas: [3], status: 'active', email: '<EMAIL>', lastLogin: null },
            'member1': { password: 'admin', role: 'member', name: 'Faculty Member 1', areas: [], status: 'active', email: '<EMAIL>', lastLogin: null },
            'member2': { password: 'admin', role: 'member', name: 'Faculty Member 2', areas: [], status: 'active', email: '<EMAIL>', lastLogin: null },
            'member3': { password: 'admin', role: 'member', name: 'Faculty Member 3', areas: [], status: 'active', email: '<EMAIL>', lastLogin: null }
        };
        localStorage.setItem('adamsSystemUsers', JSON.stringify(defaultUsers));
    }
}

// Get system data
function getSystemData() {
    return JSON.parse(localStorage.getItem('adamsSystemData') || '{}');
}

// Save system data
function saveSystemData(data) {
    localStorage.setItem('adamsSystemData', JSON.stringify(data));
}

// Get system users
function getSystemUsers() {
    return JSON.parse(localStorage.getItem('adamsSystemUsers') || '{}');
}

// Save system users
function saveSystemUsers(users) {
    localStorage.setItem('adamsSystemUsers', JSON.stringify(users));
}

// Authentication functions
function getCurrentUser() {
    const user = sessionStorage.getItem('currentUser');
    return user ? JSON.parse(user) : null;
}

function setCurrentUser(userData) {
    sessionStorage.setItem('currentUser', JSON.stringify(userData));
}

function updateUserLastLogin(username) {
    const users = getSystemUsers();
    if (users[username]) {
        users[username].lastLogin = new Date().toISOString();
        saveSystemUsers(users);
    }
}

// Navigation functions
function getNavigationForRole(role) {
    const baseNav = [
        { href: getDashboardForRole(role), icon: 'fas fa-tachometer-alt', text: 'Dashboard', active: true }
    ];

    switch(role) {
        case 'superadmin':
            return baseNav.concat([
                { href: 'schedules.html', icon: 'fas fa-calendar', text: 'Schedules' },
                { href: 'departments.html', icon: 'fas fa-graduation-cap', text: 'Programs' },
                { href: 'documents.html', icon: 'fas fa-folder', text: 'Documents' },
                { href: 'cycles.html', icon: 'fas fa-sync', text: 'Cycle Tracker' },
                { href: 'reports.html', icon: 'fas fa-chart-bar', text: 'Reports' },
                { href: 'notifications.html', icon: 'fas fa-bell', text: 'Notifications' },
                { href: 'users.html', icon: 'fas fa-users', text: 'Users' },
                { href: 'settings.html', icon: 'fas fa-cog', text: 'Settings' }
            ]);
            
        case 'admin':
            return baseNav.concat([
                { href: 'schedules.html', icon: 'fas fa-calendar', text: 'Schedules' },
                { href: 'departments.html', icon: 'fas fa-graduation-cap', text: 'Programs' },
                { href: 'documents.html', icon: 'fas fa-folder', text: 'Documents' },
                { href: 'cycles.html', icon: 'fas fa-sync', text: 'Cycle Tracker' },
                { href: 'reports.html', icon: 'fas fa-chart-bar', text: 'Reports' },
                { href: 'notifications.html', icon: 'fas fa-bell', text: 'Notifications' },
                { href: 'users.html', icon: 'fas fa-users', text: 'Users' },
                { href: 'settings.html', icon: 'fas fa-cog', text: 'Settings' }
            ]);
            
        case 'areachair':
            return baseNav.concat([
                { href: 'documents.html', icon: 'fas fa-folder', text: 'Documents' },
                { href: 'reports.html', icon: 'fas fa-chart-bar', text: 'Reports' },
                { href: 'notifications.html', icon: 'fas fa-bell', text: 'Notifications' }
            ]);
            
        case 'subareachair':
            return baseNav.concat([
                { href: 'documents.html', icon: 'fas fa-folder', text: 'Documents' },
                { href: 'notifications.html', icon: 'fas fa-bell', text: 'Notifications' }
            ]);
            
        case 'member':
            return baseNav.concat([
                { href: '#', icon: 'fas fa-folder', text: 'My Documents', onclick: 'viewMyDocuments()' },
                { href: 'notifications.html', icon: 'fas fa-bell', text: 'Notifications' }
            ]);
            
        default:
            return baseNav;
    }
}

function getDashboardForRole(role) {
    switch(role) {
        case 'superadmin':
        case 'admin':
            return 'index.html';
        case 'areachair':
            return 'dashboard-areachair.html';
        case 'subareachair':
            return 'dashboard-subareachair.html';
        case 'member':
            return 'dashboard-member.html';
        default:
            return 'index.html';
    }
}

// Logout function
function logoutUser() {
    if (confirm('Are you sure you want to logout?')) {
        sessionStorage.removeItem('currentUser');
        showToast('Logged out successfully!', 'success');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1000);
    }
}

// Update navigation based on user role
function updateNavigationForCurrentUser() {
    const user = getCurrentUser();
    if (!user) return;

    // Update user display name in all relevant elements with various IDs
    const userDisplayElements = document.querySelectorAll('#userDisplayName, .user-display-name, #currentUserName, [data-user-name]');
    userDisplayElements.forEach(element => {
        if (element) {
            // Show just the user's name without role indicators
            element.textContent = user.name;
        }
    });

    // Update navigation menu based on role (for non-admin/superadmin users)
    const navContainer = document.querySelector('.navbar-nav.me-auto');
    if (navContainer && user.role !== 'superadmin' && user.role !== 'admin') {
        const navigation = getNavigationForRole(user.role);
        const currentPage = window.location.pathname.split('/').pop();
        
        navContainer.innerHTML = navigation.map(item => {
            const isActive = item.href === currentPage || (item.active && currentPage === getDashboardForRole(user.role));
            return `
                <li class="nav-item">
                    <a class="nav-link ${isActive ? 'active' : ''}" href="${item.href}" ${item.onclick ? `onclick="${item.onclick}"` : ''}>
                        <i class="${item.icon} me-1"></i>${item.text}
                    </a>
                </li>
            `;
        }).join('');
    }

    // Ensure logout functionality is available for all users
    const logoutLinks = document.querySelectorAll('a[href="#"][onclick*="logout"], .logout-btn, a[onclick*="logoutUser"]');
    logoutLinks.forEach(link => {
        link.onclick = function(e) {
            e.preventDefault();
            logoutUser();
        };
    });
}

// Check authentication and redirect if necessary
function checkAuthenticationAndRedirect() {
    const user = getCurrentUser();
    if (!user) {
        window.location.href = 'login.html';
        return false;
    }

    const currentPage = window.location.pathname.split('/').pop();
    const expectedDashboard = getDashboardForRole(user.role);
    
    // If user is on a dashboard page that doesn't match their role, redirect
    if (currentPage.includes('dashboard-') && currentPage !== expectedDashboard) {
        window.location.href = expectedDashboard;
        return false;
    }

    return user;
}

// Initialize page based on user role
function initializePage() {
    initializeSystemDataWithSync();
    const user = checkAuthenticationAndRedirect();
    if (user) {
        updateNavigationForCurrentUser();
        currentUser = user;
    }
}

// Utility Functions
function generateId(array) {
    return array.length > 0 ? Math.max(...array.map(item => item.id)) + 1 : 1;
}

function formatDate(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

function getEntityById(array, id) {
    return array.find(item => item.id === parseInt(id));
}

function showToast(message, type = 'success') {
    const toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1056';
        document.body.appendChild(container);
    }

    const toastHtml = `
        <div class="toast align-items-center text-bg-${type}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    const toastElement = document.createElement('div');
    toastElement.innerHTML = toastHtml;
    document.getElementById('toastContainer').appendChild(toastElement.firstElementChild);

    const toast = new bootstrap.Toast(toastElement.firstElementChild);
    toast.show();

    setTimeout(() => {
        toastElement.firstElementChild.remove();
    }, 5000);
}

// Search and Filter Functions
function searchTable(searchTerm, tableBodyId) {
    const tableBody = document.getElementById(tableBodyId);
    const rows = tableBody.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        let found = false;

        for (let j = 0; j < cells.length; j++) {
            const cellText = cells[j].textContent.toLowerCase();
            if (cellText.includes(searchTerm.toLowerCase())) {
                found = true;
                break;
            }
        }

        row.style.display = found ? '' : 'none';
    }
}

function filterByStatus(status, tableBodyId) {
    const tableBody = document.getElementById(tableBodyId);
    const rows = tableBody.getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const statusCell = row.querySelector('.status-cell');
        
        if (status === 'all' || (statusCell && statusCell.textContent.trim() === status)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }
}

// Modal Functions
function openModal(modalId) {
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();
}

function closeModal(modalId) {
    const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
    if (modal) {
        modal.hide();
    }
}

// Form Functions
function resetForm(formId) {
    document.getElementById(formId).reset();
    // Clear any error messages
    const errorElements = document.querySelectorAll(`#${formId} .error-message`);
    errorElements.forEach(element => element.remove());
}

function validateForm(formId) {
    const form = document.getElementById(formId);
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        const errorId = `${field.id}-error`;
        const existingError = document.getElementById(errorId);
        
        if (existingError) {
            existingError.remove();
        }

        if (!field.value.trim()) {
            isValid = false;
            const errorElement = document.createElement('div');
            errorElement.id = errorId;
            errorElement.className = 'error-message text-danger small mt-1';
            errorElement.textContent = 'This field is required.';
            field.parentNode.appendChild(errorElement);
            field.classList.add('is-invalid');
        } else {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        }
    });

    return isValid;
}

// Loading States
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.add('loading');
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border spinner-border-sm me-2';
        spinner.setAttribute('role', 'status');
        element.insertBefore(spinner, element.firstChild);
    }
}

function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.remove('loading');
        const spinner = element.querySelector('.spinner-border');
        if (spinner) {
            spinner.remove();
        }
    }
}

// File Upload Functions
function handleFileUpload(inputElement, allowedTypes = ['.pdf', '.doc', '.docx', '.xlsx', '.jpg', '.png']) {
    const files = inputElement.files;
    const fileList = document.getElementById('fileList');
    
    if (fileList) {
        fileList.innerHTML = '';
    }

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            showToast(`File type ${fileExtension} is not allowed.`, 'error');
            continue;
        }

        const fileItem = document.createElement('div');
        fileItem.className = 'file-item d-flex justify-content-between align-items-center p-2 border rounded mb-2';
        fileItem.innerHTML = `
            <div>
                <i class="fas fa-file me-2"></i>
                <span>${file.name}</span>
                <small class="text-muted">(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(this)">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        if (fileList) {
            fileList.appendChild(fileItem);
        }
    }
}

function removeFile(button) {
    button.closest('.file-item').remove();
}

// Drag and Drop for File Upload
function setupDragAndDrop(dropZoneId, fileInputId) {
    const dropZone = document.getElementById(dropZoneId);
    const fileInput = document.getElementById(fileInputId);

    if (!dropZone || !fileInput) return;

    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        fileInput.files = files;
        handleFileUpload(fileInput);
    });

    dropZone.addEventListener('click', () => {
        fileInput.click();
    });
}

// Export Functions
function exportToCSV(data, filename) {
    const csvContent = convertToCSV(data);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

function convertToCSV(data) {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];
    
    data.forEach(row => {
        const values = headers.map(header => {
            const value = row[header];
            return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
        });
        csvRows.push(values.join(','));
    });
    
    return csvRows.join('\n');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    
    // Set up logout functionality for all logout links
    document.addEventListener('click', function(e) {
        if (e.target.matches('a[href="#"][onclick*="logout"], .logout-btn') || 
            e.target.closest('a[href="#"][onclick*="logout"], .logout-btn')) {
            e.preventDefault();
            logoutUser();
        }
    });
});

// Global utility functions that might be used across different pages
window.getCurrentUser = getCurrentUser;
window.setCurrentUser = setCurrentUser;
window.updateUserLastLogin = updateUserLastLogin;
window.getSystemData = getSystemData;
window.saveSystemData = saveSystemData;
window.getSystemUsers = getSystemUsers;
window.saveSystemUsers = saveSystemUsers;
window.logoutUser = logoutUser;
window.showToast = showToast;
window.validateForm = validateForm;
window.downloadCSV = downloadCSV;

// CENTRALIZED DATA MANAGEMENT FOR ROLE SYNCHRONIZATION

// Save all data to localStorage with proper structure
function saveAllDataToStorage() {
    const centralData = {
        departments,
        programs,
        agencies,
        levels,
        cycles,
        schedules,
        documents,
        activityLog,
        lastSync: new Date().toISOString()
    };
    localStorage.setItem('adamsSystemData', JSON.stringify(centralData));
}

// Load all data from localStorage
function loadAllDataFromStorage() {
    const stored = localStorage.getItem('adamsSystemData');
    if (stored) {
        const centralData = JSON.parse(stored);
        if (centralData.departments) departments = centralData.departments;
        if (centralData.programs) programs = centralData.programs;
        if (centralData.agencies) agencies = centralData.agencies;
        if (centralData.levels) levels = centralData.levels;
        if (centralData.cycles) cycles = centralData.cycles;
        if (centralData.schedules) schedules = centralData.schedules;
        if (centralData.documents) documents = centralData.documents;
        if (centralData.activityLog) activityLog = centralData.activityLog;
    }
}

// Add activity log entry
function addActivityLog(action, description, entityType = null, entityId = null) {
    const user = getCurrentUser();
    if (!user) return;

    const logEntry = {
        id: generateId(activityLog),
        action,
        description,
        userId: user.username || 'system',
        userName: user.name || 'System',
        timestamp: new Date().toISOString(),
        entityType,
        entityId
    };
    
    activityLog.unshift(logEntry); // Add to beginning for newest first
    
    // Keep only last 100 entries to prevent storage bloat
    if (activityLog.length > 100) {
        activityLog = activityLog.slice(0, 100);
    }
    
    saveAllDataToStorage();
}

// Get user's accessible data based on role and areas
function getUserAccessibleData(user, dataType) {
    if (!user) return [];
    
    switch (user.role) {
        case 'superadmin':
        case 'admin':
            // Full access to all data
            switch (dataType) {
                case 'schedules': return schedules;
                case 'documents': return documents;
                case 'programs': return programs;
                case 'departments': return departments;
                case 'activityLog': return activityLog;
                default: return [];
            }
            
        case 'areachair':
            // Access to data for their assigned areas
            switch (dataType) {
                case 'schedules': return schedules.filter(s => {
                    const program = getEntityById(programs, s.programId);
                    return program && user.areas.includes(program.departmentId);
                });
                case 'documents': return documents.filter(d => 
                    user.areas.includes(d.area) || d.uploadedBy === user.username || d.reviewedBy === user.username
                );
                case 'programs': return programs.filter(p => user.areas.includes(p.departmentId));
                case 'departments': return departments.filter(d => user.areas.includes(d.id));
                case 'activityLog': return activityLog.filter(log => {
                    // Show activities related to their areas or their own activities
                    return log.userId === user.username || 
                           (log.entityType === 'schedule' && getUserAccessibleData(user, 'schedules').some(s => s.id === log.entityId)) ||
                           (log.entityType === 'document' && getUserAccessibleData(user, 'documents').some(d => d.id === log.entityId));
                });
                default: return [];
            }
            
        case 'subareachair':
            // Access to data for their specific area
            switch (dataType) {
                case 'schedules': return schedules.filter(s => {
                    const program = getEntityById(programs, s.programId);
                    return program && user.areas.includes(program.departmentId);
                });
                case 'documents': return documents.filter(d => 
                    user.areas.includes(d.area) || d.uploadedBy === user.username || d.reviewedBy === user.username
                );
                case 'programs': return programs.filter(p => user.areas.includes(p.departmentId));
                case 'departments': return departments.filter(d => user.areas.includes(d.id));
                case 'activityLog': return activityLog.filter(log => 
                    log.userId === user.username ||
                    (log.entityType === 'document' && getUserAccessibleData(user, 'documents').some(d => d.id === log.entityId))
                );
                default: return [];
            }
            
        case 'member':
            // Limited access - mainly their own documents and related schedules
            switch (dataType) {
                case 'documents': return documents.filter(d => d.uploadedBy === user.username);
                case 'schedules': return schedules; // Can view all schedules for reference
                case 'programs': return programs; // Can view all programs for reference
                case 'departments': return departments; // Can view all departments for reference
                case 'activityLog': return activityLog.filter(log => log.userId === user.username);
                default: return [];
            }
            
        default:
            return [];
    }
}

// Enhanced CRUD operations with role tracking and data sync

// Create schedule with role tracking
function createScheduleWithTracking(scheduleData) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const newSchedule = {
        ...scheduleData,
        id: generateId(schedules),
        createdBy: user.username || 'system',
        createdDate: new Date().toISOString(),
        lastModifiedBy: user.username || 'system',
        lastModifiedDate: new Date().toISOString()
    };
    
    schedules.push(newSchedule);
    addActivityLog('Schedule Created', `Created schedule for ${getEntityById(programs, newSchedule.programId)?.name || 'Unknown Program'}`, 'schedule', newSchedule.id);
    saveAllDataToStorage();
    
    return newSchedule;
}

// Update schedule with role tracking
function updateScheduleWithTracking(scheduleId, updateData) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const scheduleIndex = schedules.findIndex(s => s.id === scheduleId);
    if (scheduleIndex === -1) return false;
    
    const oldSchedule = { ...schedules[scheduleIndex] };
    schedules[scheduleIndex] = {
        ...schedules[scheduleIndex],
        ...updateData,
        lastModifiedBy: user.username || 'system',
        lastModifiedDate: new Date().toISOString()
    };
    
    addActivityLog('Schedule Updated', `Updated schedule for ${getEntityById(programs, schedules[scheduleIndex].programId)?.name || 'Unknown Program'}`, 'schedule', scheduleId);
    saveAllDataToStorage();
    
    return schedules[scheduleIndex];
}

// Delete schedule with role tracking
function deleteScheduleWithTracking(scheduleId) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const schedule = getEntityById(schedules, scheduleId);
    if (!schedule) return false;
    
    schedules = schedules.filter(s => s.id !== scheduleId);
    addActivityLog('Schedule Deleted', `Deleted schedule for ${getEntityById(programs, schedule.programId)?.name || 'Unknown Program'}`, 'schedule', scheduleId);
    saveAllDataToStorage();
    
    return true;
}

// Create document with role tracking
function createDocumentWithTracking(documentData) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const newDocument = {
        ...documentData,
        id: generateId(documents),
        uploadedBy: user.username || 'system',
        uploadedDate: new Date().toISOString(),
        status: 'Pending Review'
    };
    
    documents.push(newDocument);
    addActivityLog('Document Uploaded', `Uploaded ${newDocument.title}`, 'document', newDocument.id);
    saveAllDataToStorage();
    
    return newDocument;
}

// Update document with role tracking
function updateDocumentWithTracking(documentId, updateData) {
    const user = getCurrentUser();
    if (!user) return false;
    
    const documentIndex = documents.findIndex(d => d.id === documentId);
    if (documentIndex === -1) return false;
    
    const oldStatus = documents[documentIndex].status;
    documents[documentIndex] = {
        ...documents[documentIndex],
        ...updateData
    };
    
    // Add specific tracking for status changes
    if (updateData.status && updateData.status !== oldStatus) {
        if (updateData.status === 'In Review') {
            documents[documentIndex].reviewedBy = user.username;
            documents[documentIndex].reviewDate = new Date().toISOString();
            addActivityLog('Document Review Started', `Started reviewing ${documents[documentIndex].title}`, 'document', documentId);
        } else if (updateData.status === 'Approved') {
            documents[documentIndex].approvedBy = user.username;
            documents[documentIndex].approvalDate = new Date().toISOString();
            addActivityLog('Document Approved', `Approved ${documents[documentIndex].title}`, 'document', documentId);
        } else if (updateData.status === 'Rejected') {
            addActivityLog('Document Rejected', `Rejected ${documents[documentIndex].title}`, 'document', documentId);
        }
    }
    
    saveAllDataToStorage();
    return documents[documentIndex];
}

// Get recent activity for dashboard
function getRecentActivity(limit = 10) {
    const user = getCurrentUser();
    if (!user) return [];
    
    return getUserAccessibleData(user, 'activityLog').slice(0, limit);
}

// Initialize data loading
function initializeSystemDataWithSync() {
    initializeSystemData();
    loadAllDataFromStorage();
}

// Export functions for global use
window.documents = documents;
window.activityLog = activityLog;
window.saveAllDataToStorage = saveAllDataToStorage;
window.loadAllDataFromStorage = loadAllDataFromStorage;
window.addActivityLog = addActivityLog;
window.getUserAccessibleData = getUserAccessibleData;
window.createScheduleWithTracking = createScheduleWithTracking;
window.updateScheduleWithTracking = updateScheduleWithTracking;
window.deleteScheduleWithTracking = deleteScheduleWithTracking;
window.createDocumentWithTracking = createDocumentWithTracking;
window.updateDocumentWithTracking = updateDocumentWithTracking;
window.getRecentActivity = getRecentActivity;
window.initializeSystemDataWithSync = initializeSystemDataWithSync; 