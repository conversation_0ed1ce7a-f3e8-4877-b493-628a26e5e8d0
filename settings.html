<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-cog me-3"></i>System Settings</h1>
                        <p>Configure system preferences and account settings</p>
                    </div>
                    <div>
                        <button class="btn btn-danger" onclick="logoutUser()">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Content -->
            <div class="row">
                <!-- User Account Settings -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-user me-2"></i>Account Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <label class="col-sm-4 col-form-label">Current User:</label>
                                <div class="col-sm-8">
                                    <p class="form-control-plaintext" id="currentUserDisplay">Loading...</p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label class="col-sm-4 col-form-label">Role:</label>
                                <div class="col-sm-8">
                                    <p class="form-control-plaintext" id="currentRoleDisplay">Loading...</p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label class="col-sm-4 col-form-label">Access Areas:</label>
                                <div class="col-sm-8">
                                    <p class="form-control-plaintext" id="currentAreasDisplay">Loading...</p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <label class="col-sm-4 col-form-label">Session Status:</label>
                                <div class="col-sm-8">
                                    <span class="badge bg-success">Active</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary me-2" onclick="changePassword()">
                                <i class="fas fa-key me-2"></i>Change Password
                            </button>
                            <button class="btn btn-outline-danger" onclick="logoutUser()">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </button>
                        </div>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-cogs me-2"></i>System Preferences</h5>
                        </div>
                        <div class="card-body">
                            <form id="systemSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">Theme</label>
                                    <select class="form-select" id="themeSelect">
                                        <option value="light">Light Theme</option>
                                        <option value="dark">Dark Theme</option>
                                        <option value="auto">Auto (System)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Language</label>
                                    <select class="form-select" id="languageSelect">
                                        <option value="en">English</option>
                                        <option value="fil">Filipino</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Items per Page</label>
                                    <select class="form-select" id="itemsPerPage">
                                        <option value="10">10 items</option>
                                        <option value="25" selected>25 items</option>
                                        <option value="50">50 items</option>
                                        <option value="100">100 items</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoSave" checked>
                                        <label class="form-check-label" for="autoSave">
                                            Enable auto-save
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifications" checked>
                                        <label class="form-check-label" for="notifications">
                                            Enable notifications
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-success" onclick="saveSettings()">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <button class="btn btn-outline-primary w-100 mb-2" onclick="switchUser()">
                                        <i class="fas fa-user-friends me-2"></i>Switch User
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-info w-100 mb-2" onclick="clearCache()">
                                        <i class="fas fa-broom me-2"></i>Clear Cache
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-warning w-100 mb-2" onclick="exportSettings()">
                                        <i class="fas fa-download me-2"></i>Export Settings
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-success w-100 mb-2" onclick="viewSystemInfo()">
                                        <i class="fas fa-info-circle me-2"></i>System Info
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Authentication check
        function checkAuthentication() {
            const currentUser = sessionStorage.getItem('currentUser');
            if (!currentUser) {
                window.location.href = 'login.html';
                return false;
            }
            return JSON.parse(currentUser);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const user = checkAuthentication();
            if (user) {
                displayUserInfo(user);
                loadSettings();
            }
        });

        // Display user information
        function displayUserInfo(user) {
            document.getElementById('currentUserDisplay').textContent = user.name;
            document.getElementById('currentRoleDisplay').innerHTML = 
                `<span class="badge ${getRoleBadgeClass(user.role)}">${formatRole(user.role)}</span>`;
            
            let areasText = 'No specific areas';
            if (user.areas === 'all') {
                areasText = 'All Areas (Full Access)';
            } else if (Array.isArray(user.areas) && user.areas.length > 0) {
                areasText = `Areas: ${user.areas.join(', ')}`;
            }
            document.getElementById('currentAreasDisplay').textContent = areasText;
        }

        function getRoleBadgeClass(role) {
            switch(role) {
                case 'superadmin': return 'bg-warning text-dark';
                case 'admin': return 'bg-primary';
                case 'areachair': return 'bg-success';
                case 'subareachair': return 'bg-info';
                case 'member': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function formatRole(role) {
            switch(role) {
                case 'superadmin': return 'Super Administrator';
                case 'admin': return 'Administrator';
                case 'areachair': return 'Area Chair';
                case 'subareachair': return 'Sub-Area Chair';
                case 'member': return 'Member';
                default: return 'Unknown';
            }
        }

        // Logout functionality
        function logoutUser() {
            if (confirm('Are you sure you want to logout?')) {
                // Clear session storage
                sessionStorage.removeItem('currentUser');
                
                // Show logout message
                showToast('Logged out successfully!', 'success');
                
                // Redirect to login after a brief delay
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
            }
        }

        // Switch user functionality
        function switchUser() {
            if (confirm('Are you sure you want to switch user? This will log you out.')) {
                sessionStorage.removeItem('currentUser');
                window.location.href = 'login.html';
            }
        }

        // Settings functions
        function loadSettings() {
            // Load saved settings from localStorage or use defaults
            const settings = JSON.parse(localStorage.getItem('systemSettings')) || {};
            
            document.getElementById('themeSelect').value = settings.theme || 'light';
            document.getElementById('languageSelect').value = settings.language || 'en';
            document.getElementById('itemsPerPage').value = settings.itemsPerPage || '25';
            document.getElementById('autoSave').checked = settings.autoSave !== false;
            document.getElementById('notifications').checked = settings.notifications !== false;
        }

        function saveSettings() {
            const settings = {
                theme: document.getElementById('themeSelect').value,
                language: document.getElementById('languageSelect').value,
                itemsPerPage: document.getElementById('itemsPerPage').value,
                autoSave: document.getElementById('autoSave').checked,
                notifications: document.getElementById('notifications').checked
            };
            
            localStorage.setItem('systemSettings', JSON.stringify(settings));
            showToast('Settings saved successfully!', 'success');
        }

        function changePassword() {
            const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
            showToast(`Password change for ${currentUser.name} - Feature coming soon!`, 'info');
        }

        function clearCache() {
            if (confirm('Are you sure you want to clear the cache? This will refresh some data.')) {
                // Clear specific cache items but keep user session
                localStorage.removeItem('systemSettings');
                showToast('Cache cleared successfully!', 'success');
                
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }

        function exportSettings() {
            const settings = JSON.parse(localStorage.getItem('systemSettings')) || {};
            const user = JSON.parse(sessionStorage.getItem('currentUser'));
            
            const exportData = {
                user: user.username,
                role: user.role,
                settings: settings,
                exportDate: new Date().toISOString()
            };
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `adams_settings_${user.username}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            showToast('Settings exported successfully!', 'success');
        }

        function viewSystemInfo() {
            const user = JSON.parse(sessionStorage.getItem('currentUser'));
            const settings = JSON.parse(localStorage.getItem('systemSettings')) || {};
            
            const info = `ADAMS System Information
            
Version: 1.0.0
Current User: ${user.name} (${user.username})
Role: ${formatRole(user.role)}
Browser: ${navigator.userAgent.split(' ').slice(-2).join(' ')}
Screen Resolution: ${screen.width}x${screen.height}
Local Storage Used: ${JSON.stringify(localStorage).length} characters
Session Active Since: ${new Date().toLocaleString()}
Theme: ${settings.theme || 'Light'}
Language: ${settings.language || 'English'}`;
            
            alert(info);
        }
    </script>
</body>
</html> 