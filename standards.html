<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quality Standards - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="true">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse show" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link active" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="userDisplayName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="settings.html"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-star me-3"></i>Quality Standards Management</h1>
                        <p>Manage accreditation areas, standards, and scoring systems</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-info me-2" onclick="exportStandards()">
                            <i class="fas fa-download me-2"></i>Export Standards
                        </button>
                        <button class="btn btn-nd-green" onclick="openAddStandardModal()">
                            <i class="fas fa-plus me-2"></i>Add Standard
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="areas-overview-tab" data-bs-toggle="tab" data-bs-target="#areas-overview" type="button" role="tab">
                        <i class="fas fa-th-large me-2"></i>Areas Overview
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="standards-detail-tab" data-bs-toggle="tab" data-bs-target="#standards-detail" type="button" role="tab">
                        <i class="fas fa-list-ul me-2"></i>Standards Detail
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="scoring-system-tab" data-bs-toggle="tab" data-bs-target="#scoring-system" type="button" role="tab">
                        <i class="fas fa-calculator me-2"></i>Scoring System
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Areas Overview Tab -->
                <div class="tab-pane fade show active" id="areas-overview" role="tabpanel">
                    <div class="row" id="areasContainer">
                        <!-- Area cards will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Standards Detail Tab -->
                <div class="tab-pane fade" id="standards-detail" role="tabpanel">
                    <!-- Standards Controls -->
                    <div class="search-filter-bar">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <select class="form-select" id="filterArea" onchange="filterStandards()">
                                    <option value="all">All Areas</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterStatus" onchange="filterStandards()">
                                    <option value="all">All Status</option>
                                    <option value="Active">Active</option>
                                    <option value="Draft">Draft</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="searchStandards" placeholder="Search standards..." onkeyup="searchStandards()">
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-success" onclick="bulkActivateStandards()">
                                    <i class="fas fa-check me-2"></i>Bulk Activate
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Standards Table -->
                    <div class="data-table-container">
                        <div class="data-table-header">
                            <h5 class="mb-0"><i class="fas fa-list-ul me-2"></i>Quality Standards Repository</h5>
                        </div>
                        <div class="data-table-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                            <th>Standard #</th>
                                            <th>Area</th>
                                            <th>Title</th>
                                            <th>Description</th>
                                            <th>Weight</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="standardsTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scoring System Tab -->
                <div class="tab-pane fade" id="scoring-system" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><i class="fas fa-cog me-2"></i>Scoring Configuration</h5>
                                </div>
                                <div class="card-body">
                                    <form id="scoringConfigForm">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Scoring Method</label>
                                                <select class="form-select" id="scoringMethod">
                                                    <option value="narrative">Narrative-based</option>
                                                    <option value="point-based">Point-based</option>
                                                    <option value="hybrid">Hybrid</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Maximum Score per Standard</label>
                                                <input type="number" class="form-control" id="maxScore" value="100">
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Excellent (90-100%)</label>
                                                <input type="text" class="form-control" id="excellentLabel" value="Exceeds Standards">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Satisfactory (70-89%)</label>
                                                <input type="text" class="form-control" id="satisfactoryLabel" value="Meets Standards">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Needs Improvement (<70%)</label>
                                                <input type="text" class="form-control" id="improvementLabel" value="Below Standards">
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-primary" onclick="saveScoringConfig()">
                                            <i class="fas fa-save me-2"></i>Save Configuration
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>Area Weights</h5>
                                </div>
                                <div class="card-body">
                                    <div id="areaWeights">
                                        <!-- Area weights will be populated by JavaScript -->
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm w-100 mt-3" onclick="resetWeights()">
                                        <i class="fas fa-undo me-2"></i>Reset to Equal Weights
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Standard Modal -->
    <div class="modal fade" id="addStandardModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Standard</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addStandardForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Standard Number <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="standardNumber" min="1" max="30" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Area <span class="text-danger">*</span></label>
                                    <select class="form-select" id="standardArea" required>
                                        <option value="">Select Area</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="standardTitle" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="standardDescription" rows="4" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Weight (%)</label>
                                    <input type="number" class="form-control" id="standardWeight" min="1" max="100" value="10">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-select" id="standardStatus">
                                        <option value="Active">Active</option>
                                        <option value="Draft">Draft</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-nd-green" onclick="saveStandard()">
                        <i class="fas fa-save me-2"></i>Save Standard
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Area Details Modal -->
    <div class="modal fade" id="viewAreaModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Area Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="viewAreaDetails">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Standard Details Modal -->
    <div class="modal fade" id="viewStandardModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-eye me-2"></i>Standard Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="viewStandardDetails">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Standard Modal -->
    <div class="modal fade" id="editStandardModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Standard</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editStandardForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStandardNumber" class="form-label">Standard Number <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="editStandardNumber" required min="1" max="30">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStandardArea" class="form-label">Area <span class="text-danger">*</span></label>
                                    <select class="form-control" id="editStandardArea" required>
                                        <option value="">Select Area</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="editStandardTitle" class="form-label">Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editStandardTitle" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="editStandardDescription" class="form-label">Description</label>
                                    <textarea class="form-control" id="editStandardDescription" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStandardWeight" class="form-label">Weight (%) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="editStandardWeight" required min="0" max="100">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editStandardStatus" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-control" id="editStandardStatus" required>
                                        <option value="Active">Active</option>
                                        <option value="Draft">Draft</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="updateStandard()">
                        <i class="fas fa-save me-1"></i>Update Standard
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        // Page-specific JavaScript for Quality Standards
        let qualityAreas = [];
        let qualityStandards = [];
        let scoringConfig = {};
        let currentEditingStandardId = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeStandardsData();
            loadAreasOverview();
            loadStandardsDetail();
            loadScoringSystem();
            populateDropdowns();
        });

        // Initialize quality standards data
        function initializeStandardsData() {
            if (qualityAreas.length === 0) {
                qualityAreas = [
                    { id: 1, name: 'Leadership and Governance', description: 'Vision-Mission, Leadership Management, Strategic Management, Policy Formulation and Implementation', standardCount: 4, weight: 12.5 },
                    { id: 2, name: 'Quality Assurance', description: 'Internal Quality Assurance System, External Quality Assurance', standardCount: 2, weight: 12.5 },
                    { id: 3, name: 'Resource Management', description: 'Human Resources, Financial Resources, Physical Facilities', standardCount: 4, weight: 12.5 },
                    { id: 4, name: 'Teaching-Learning', description: 'Curricular Programs, Teaching and Learning Methods, Assessment Methods', standardCount: 3, weight: 12.5 },
                    { id: 5, name: 'Student Services', description: 'Student Recruitment, Admission, and Placement, Student Services Programs and Support', standardCount: 2, weight: 12.5 },
                    { id: 6, name: 'External Relations', description: 'Networks, Linkages, and Partnerships, Community Engagement and Service', standardCount: 2, weight: 12.5 },
                    { id: 7, name: 'Research', description: 'Research Management and Collaboration, Intellectual Property Rights and Ethics in Research', standardCount: 2, weight: 12.5 },
                    { id: 8, name: 'Results', description: 'Educational Results, Community Engagement and Service Results, Research Results, Financial and Competitiveness Results', standardCount: 4, weight: 12.5 }
                ];
            }

            if (qualityStandards.length === 0) {
                qualityStandards = [
                    // Area 1 - Leadership and Governance
                    { id: 1, number: 1, areaId: 1, title: 'Vision-Mission', description: 'Clear vision and mission statements that guide institutional direction', weight: 25, status: 'Active' },
                    { id: 2, number: 2, areaId: 1, title: 'Leadership Management', description: 'Effective leadership structure and management practices', weight: 25, status: 'Active' },
                    { id: 3, number: 3, areaId: 1, title: 'Strategic Management', description: 'Strategic planning and implementation processes', weight: 25, status: 'Active' },
                    { id: 4, number: 4, areaId: 1, title: 'Policy Formulation and Implementation', description: 'Development and implementation of institutional policies', weight: 25, status: 'Active' },
                    
                    // Area 2 - Quality Assurance
                    { id: 5, number: 5, areaId: 2, title: 'Risk Management', description: 'Systematic approach to identifying and managing institutional risks', weight: 50, status: 'Active' },
                    { id: 6, number: 6, areaId: 2, title: 'Internal Quality Assurance System', description: 'Comprehensive internal quality assurance mechanisms', weight: 50, status: 'Active' },
                    
                    // Area 3 - Resource Management
                    { id: 7, number: 7, areaId: 3, title: 'External Quality Assurance', description: 'Engagement with external quality assurance processes', weight: 25, status: 'Active' },
                    { id: 8, number: 8, areaId: 3, title: 'Human Resources', description: 'Faculty and staff development and management', weight: 25, status: 'Active' },
                    { id: 9, number: 9, areaId: 3, title: 'Financial Resources', description: 'Financial planning, management, and sustainability', weight: 25, status: 'Active' },
                    { id: 10, number: 10, areaId: 3, title: 'Physical Facilities', description: 'Infrastructure and physical learning environment', weight: 25, status: 'Active' },
                    
                    // Area 4 - Teaching-Learning
                    { id: 11, number: 11, areaId: 4, title: 'Curricular Programs', description: 'Design and implementation of academic curricula', weight: 33, status: 'Active' },
                    { id: 12, number: 12, areaId: 4, title: 'Teaching and Learning Methods', description: 'Effective pedagogical approaches and methodologies', weight: 34, status: 'Active' },
                    { id: 13, number: 13, areaId: 4, title: 'Assessment Methods', description: 'Student assessment and evaluation systems', weight: 33, status: 'Active' },
                    
                    // Area 5 - Student Services
                    { id: 14, number: 14, areaId: 5, title: 'Student Recruitment, Admission, and Placement', description: 'Comprehensive student recruitment, admission processes, and career placement services', weight: 50, status: 'Active' },
                    { id: 15, number: 15, areaId: 5, title: 'Student Services Programs and Support', description: 'Student welfare programs, counseling, and academic support services', weight: 50, status: 'Active' },
                    
                    // Area 6 - External Relations
                    { id: 16, number: 16, areaId: 6, title: 'Networks, Linkages, and Partnerships', description: 'Strategic partnerships with industry, government, and other educational institutions', weight: 50, status: 'Active' },
                    { id: 17, number: 17, areaId: 6, title: 'Community Engagement and Service', description: 'Community outreach programs and social responsibility initiatives', weight: 50, status: 'Active' },
                    
                    // Area 7 - Research
                    { id: 18, number: 18, areaId: 7, title: 'Research Management and Collaboration', description: 'Research program development, management, and collaborative research initiatives', weight: 50, status: 'Active' },
                    { id: 19, number: 19, areaId: 7, title: 'Intellectual Property Rights and Ethics in Research', description: 'Ethical research practices and intellectual property protection policies', weight: 50, status: 'Active' },
                    
                    // Area 8 - Results
                    { id: 20, number: 20, areaId: 8, title: 'Educational Results', description: 'Student learning outcomes, graduation rates, and academic achievement indicators', weight: 25, status: 'Active' },
                    { id: 21, number: 21, areaId: 8, title: 'Community Engagement and Service Results', description: 'Impact and outcomes of community service and engagement programs', weight: 25, status: 'Active' },
                    { id: 22, number: 22, areaId: 8, title: 'Research Results', description: 'Research output, publications, and impact on knowledge advancement', weight: 25, status: 'Active' },
                    { id: 23, number: 23, areaId: 8, title: 'Financial and Competitiveness Results', description: 'Financial sustainability and institutional competitiveness indicators', weight: 25, status: 'Active' }
                ];
            }

            if (Object.keys(scoringConfig).length === 0) {
                scoringConfig = {
                    method: 'narrative',
                    maxScore: 100,
                    excellentLabel: 'Exceeds Standards',
                    satisfactoryLabel: 'Meets Standards',
                    improvementLabel: 'Below Standards'
                };
            }
        }

        // Load areas overview
        function loadAreasOverview() {
            const container = document.getElementById('areasContainer');
            container.innerHTML = '';

            qualityAreas.forEach(area => {
                const areaStandards = qualityStandards.filter(s => s.areaId === area.id);
                const activeStandards = areaStandards.filter(s => s.status === 'Active').length;
                
                const card = `
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 area-card" onclick="viewAreaDetails(${area.id})">
                            <div class="card-header bg-primary text-white">
                                <h6 class="card-title mb-0">Area ${area.id} - ${area.name}</h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">${area.description}</p>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h4 class="mb-0 text-primary">${activeStandards}</h4>
                                        <small class="text-muted">Active Standards</small>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="mb-0 text-info">${area.weight}%</h4>
                                        <small class="text-muted">Weight</small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" onclick="event.stopPropagation(); viewAreaDetails(${area.id})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="event.stopPropagation(); editArea(${area.id})">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += card;
            });
        }

        // Load standards detail
        function loadStandardsDetail() {
            const tbody = document.getElementById('standardsTableBody');
            tbody.innerHTML = '';

            qualityStandards.forEach(standard => {
                const area = qualityAreas.find(a => a.id === standard.areaId);
                
                const row = `
                    <tr>
                        <td><input type="checkbox" class="standard-checkbox" value="${standard.id}"></td>
                        <td><strong>Standard ${standard.number}</strong></td>
                        <td><span class="badge bg-info">Area ${area.id}</span></td>
                        <td><strong>${standard.title}</strong></td>
                        <td><small>${standard.description}</small></td>
                        <td>${standard.weight}%</td>
                        <td><span class="badge ${getStatusBadgeClass(standard.status)}">${standard.status}</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewStandard(${standard.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="editStandard(${standard.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteStandard(${standard.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // Load scoring system
        function loadScoringSystem() {
            document.getElementById('scoringMethod').value = scoringConfig.method;
            document.getElementById('maxScore').value = scoringConfig.maxScore;
            document.getElementById('excellentLabel').value = scoringConfig.excellentLabel;
            document.getElementById('satisfactoryLabel').value = scoringConfig.satisfactoryLabel;
            document.getElementById('improvementLabel').value = scoringConfig.improvementLabel;
            
            loadAreaWeights();
        }

        // Load area weights
        function loadAreaWeights() {
            const container = document.getElementById('areaWeights');
            container.innerHTML = '';

            qualityAreas.forEach(area => {
                const weightHtml = `
                    <div class="mb-2">
                        <label class="form-label small">Area ${area.id}: ${area.name}</label>
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control" id="weight_${area.id}" value="${area.weight}" min="0" max="100" step="0.1">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                `;
                container.innerHTML += weightHtml;
            });
        }

        // Helper functions
        function getStatusBadgeClass(status) {
            switch(status) {
                case 'Active': return 'bg-success';
                case 'Draft': return 'bg-warning';
                case 'Inactive': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function populateDropdowns() {
            const areaSelects = ['filterArea', 'standardArea', 'editStandardArea'];
            areaSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const currentValue = select.value;
                    if (selectId === 'filterArea') {
                        select.innerHTML = '<option value="all">All Areas</option>';
                    } else {
                        select.innerHTML = '<option value="">Select Area</option>';
                    }
                    
                    qualityAreas.forEach(area => {
                        select.innerHTML += `<option value="${area.id}">Area ${area.id}: ${area.name}</option>`;
                    });
                    
                    if (currentValue) select.value = currentValue;
                }
            });
        }

        // Action functions - FULLY IMPLEMENTED
        function viewAreaDetails(id) {
            const area = qualityAreas.find(a => a.id === id);
            const areaStandards = qualityStandards.filter(s => s.areaId === id);
            
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-12">
                        <h6 class="text-primary">Area ${area.id} - ${area.name}</h6>
                        <p><strong>Description:</strong> ${area.description}</p>
                        <p><strong>Weight:</strong> ${area.weight}%</p>
                        <p><strong>Total Standards:</strong> ${areaStandards.length}</p>
                        <p><strong>Active Standards:</strong> ${areaStandards.filter(s => s.status === 'Active').length}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <h6 class="text-primary">Standards in this Area:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Standard #</th>
                                        <th>Title</th>
                                        <th>Weight</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${areaStandards.map(s => `
                                        <tr>
                                            <td>Standard ${s.number}</td>
                                            <td>${s.title}</td>
                                            <td>${s.weight}%</td>
                                            <td><span class="badge ${getStatusBadgeClass(s.status)}">${s.status}</span></td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('viewAreaDetails').innerHTML = detailsHtml;
            openModal('viewAreaModal');
        }

        function editArea(id) {
            const area = qualityAreas.find(a => a.id === id);
            const newWeight = prompt(`Enter new weight for Area ${area.id} (${area.name}):`, area.weight);
            
            if (newWeight && !isNaN(newWeight) && newWeight >= 0 && newWeight <= 100) {
                area.weight = parseFloat(newWeight);
                loadAreasOverview();
                loadAreaWeights();
                showToast(`Area ${area.id} weight updated to ${newWeight}%`);
            }
        }

        function openAddStandardModal() {
            resetForm('addStandardForm');
            populateDropdowns();
            openModal('addStandardModal');
        }

        function saveStandard() {
            if (!validateForm('addStandardForm')) return;

            const newStandard = {
                id: generateId(qualityStandards),
                number: parseInt(document.getElementById('standardNumber').value),
                areaId: parseInt(document.getElementById('standardArea').value),
                title: document.getElementById('standardTitle').value,
                description: document.getElementById('standardDescription').value,
                weight: parseInt(document.getElementById('standardWeight').value),
                status: document.getElementById('standardStatus').value
            };

            qualityStandards.push(newStandard);
            loadStandardsDetail();
            loadAreasOverview();
            closeModal('addStandardModal');
            showToast('Standard added successfully!');
        }

        function saveScoringConfig() {
            scoringConfig.method = document.getElementById('scoringMethod').value;
            scoringConfig.maxScore = parseInt(document.getElementById('maxScore').value);
            scoringConfig.excellentLabel = document.getElementById('excellentLabel').value;
            scoringConfig.satisfactoryLabel = document.getElementById('satisfactoryLabel').value;
            scoringConfig.improvementLabel = document.getElementById('improvementLabel').value;
            
            // Update area weights
            qualityAreas.forEach(area => {
                const weightInput = document.getElementById(`weight_${area.id}`);
                if (weightInput) {
                    area.weight = parseFloat(weightInput.value);
                }
            });
            
            loadAreasOverview();
            showToast('Scoring configuration saved successfully!');
        }

        function resetWeights() {
            const equalWeight = 100 / qualityAreas.length;
            qualityAreas.forEach(area => {
                area.weight = equalWeight;
                const weightInput = document.getElementById(`weight_${area.id}`);
                if (weightInput) {
                    weightInput.value = equalWeight.toFixed(1);
                }
            });
            loadAreasOverview();
            showToast('Weights reset to equal distribution!');
        }

        function exportStandards() {
            const csvData = `Quality Standards Export
Generated Date,${new Date().toLocaleString()}

Standard Number,Area,Title,Description,Weight,Status
${qualityStandards.map(s => {
                const area = qualityAreas.find(a => a.id === s.areaId);
                return `${s.number},"Area ${area.id}: ${area.name}","${s.title}","${s.description}",${s.weight}%,${s.status}`;
            }).join('\n')}`;
            
            downloadCSV(csvData, 'Quality_Standards_Export.csv');
            showToast('Standards exported successfully!');
        }

        // Helper function to download CSV
        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // Filter and search functions
        function filterStandards() { loadStandardsDetail(); }
        function searchStandards() {
            const searchTerm = document.getElementById('searchStandards').value.toLowerCase();
            const rows = document.querySelectorAll('#standardsTableBody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.standard-checkbox');
            checkboxes.forEach(cb => cb.checked = selectAll.checked);
        }

        function bulkActivateStandards() {
            const selectedIds = Array.from(document.querySelectorAll('.standard-checkbox:checked')).map(cb => parseInt(cb.value));
            if (selectedIds.length === 0) {
                showToast('Please select standards to activate', 'warning');
                return;
            }
            
            selectedIds.forEach(id => {
                const standard = qualityStandards.find(s => s.id === id);
                if (standard) standard.status = 'Active';
            });
            
            loadStandardsDetail();
            showToast(`${selectedIds.length} standards activated successfully!`);
        }

        // NEWLY IMPLEMENTED FUNCTIONS
        function viewStandard(id) { 
            const standard = qualityStandards.find(s => s.id === id);
            if (!standard) return;

            const area = qualityAreas.find(a => a.id === standard.areaId);
            
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Standard Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Standard Number:</strong></td><td>${standard.number}</td></tr>
                            <tr><td><strong>Area:</strong></td><td>Area ${area.id}: ${area.name}</td></tr>
                            <tr><td><strong>Title:</strong></td><td>${standard.title}</td></tr>
                            <tr><td><strong>Weight:</strong></td><td>${standard.weight}%</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusBadgeClass(standard.status)}">${standard.status}</span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Description</h6>
                        <p>${standard.description}</p>
                        
                        <h6 class="text-primary">Area Context</h6>
                        <p><small>${area.description}</small></p>
                    </div>
                </div>
            `;
            
            document.getElementById('viewStandardDetails').innerHTML = detailsHtml;
            openModal('viewStandardModal');
        }

        function editStandard(id) { 
            const standard = qualityStandards.find(s => s.id === id);
            if (!standard) return;

            currentEditingStandardId = id;
            
            // Populate dropdowns first
            populateDropdowns();
            
            // Populate edit form
            document.getElementById('editStandardNumber').value = standard.number;
            document.getElementById('editStandardArea').value = standard.areaId;
            document.getElementById('editStandardTitle').value = standard.title;
            document.getElementById('editStandardDescription').value = standard.description;
            document.getElementById('editStandardWeight').value = standard.weight;
            document.getElementById('editStandardStatus').value = standard.status;

            openModal('editStandardModal');
        }

        function updateStandard() {
            if (!validateForm('editStandardForm')) return;

            const standardIndex = qualityStandards.findIndex(s => s.id === currentEditingStandardId);
            if (standardIndex === -1) return;

            qualityStandards[standardIndex] = {
                ...qualityStandards[standardIndex],
                number: parseInt(document.getElementById('editStandardNumber').value),
                areaId: parseInt(document.getElementById('editStandardArea').value),
                title: document.getElementById('editStandardTitle').value,
                description: document.getElementById('editStandardDescription').value,
                weight: parseInt(document.getElementById('editStandardWeight').value),
                status: document.getElementById('editStandardStatus').value
            };

            loadStandardsDetail();
            loadAreasOverview();
            closeModal('editStandardModal');
            showToast('Standard updated successfully!');
        }

        function deleteStandard(id) { 
            const standard = qualityStandards.find(s => s.id === id);
            if (!standard) return;

            if (confirm(`Are you sure you want to delete Standard ${standard.number}: ${standard.title}?`)) {
                qualityStandards = qualityStandards.filter(s => s.id !== id);
                loadStandardsDetail();
                loadAreasOverview();
                showToast('Standard deleted successfully!');
            }
        }
    </script>

    <style>
        .area-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .area-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
    </style>
</body>
</html> 