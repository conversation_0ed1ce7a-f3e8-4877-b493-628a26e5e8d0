/* Notre Dame Green Theme Variables */
:root {
    --nd-green-primary: #1e5631;
    --nd-green-light: #2d7a3f;
    --nd-green-dark: #154223;
    --nd-green-accent: #3a8b4f;
    --nd-gold: #c19b37;
    --nd-gray-light: #f8f9fa;
    --nd-gray-medium: #6c757d;
    --nd-white: #ffffff;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--nd-gray-light);
    color: #333;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(135deg, var(--nd-green-primary) 0%, var(--nd-green-light) 100%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    color: white;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 700;
}

.sidebar-brand:hover {
    color: white;
    text-decoration: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.3s ease;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.sidebar-nav .nav {
    padding: 0;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.sidebar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
}

.sidebar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.sidebar-nav .nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar-nav .collapse .nav-link {
    padding-left: 3rem;
    font-size: 0.9rem;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0.375rem;
}

.sidebar-footer .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Mobile Sidebar Toggle */
.sidebar-mobile-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background-color: var(--nd-green-primary);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 1.2rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.sidebar-mobile-toggle:hover {
    background-color: var(--nd-green-dark);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Main Content */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding-top: 5rem;
    }
    
    .sidebar-mobile-toggle {
        display: block;
    }
}

@media (min-width: 992px) {
    .sidebar-mobile-toggle {
        display: none;
    }
    
    .sidebar-overlay {
        display: none;
    }
}

/* Text Colors */
.text-nd-green {
    color: var(--nd-green-primary) !important;
}

/* Stat Card Styles */
.stat-card {
    background: #f5f5f5;
    border-radius: 1rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075), 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
    overflow: hidden;
    min-height: 100px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--nd-green-primary) 0%, var(--nd-green-light) 100%);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15), 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.stat-card.bg-primary::before {
    background: linear-gradient(90deg, #a8d5ba 0%, #c8e6c9 100%);
}

.stat-card.bg-success::before {
    background: linear-gradient(90deg, #b3e5fc 0%, #81d4fa 100%);
}

.stat-card.bg-warning::before {
    background: linear-gradient(90deg, #ffcc80 0%, #ffb74d 100%);
}

.stat-card.bg-info::before {
    background: linear-gradient(90deg, #ffab91 0%, #ff8a65 100%);
}

.stat-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
}

.stat-card.bg-primary .stat-icon {
    background: linear-gradient(135deg, #a8d5ba 0%, #c8e6c9 100%);
}
.stat-card.bg-primary {
    background-color: #e6f5e6;
}

.stat-card.bg-success .stat-icon {
    background: linear-gradient(135deg, #b3e5fc 0%, #81d4fa 100%);
}
.stat-card.bg-success {
    background-color: #e0f7ff;
}

.stat-card.bg-warning .stat-icon {
    background: linear-gradient(135deg, #ffcc80 0%, #ffb74d 100%);
}
.stat-card.bg-warning {
    background-color: #fff4e0;
}

.stat-card.bg-info .stat-icon {
    background: linear-gradient(135deg, #ffab91 0%, #ff8a65 100%);
}
.stat-card.bg-info {
    background-color: #ffe5e0;
}

.stat-icon i {
    font-size: 2.2rem;
    color: #222222;
    text-shadow: none;
}

.stat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-content h3 {
    font-size: 4rem;
    font-weight: 900;
    margin: 0;
    color: #000000;
    line-height: 1;
}

.stat-content p {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
    color: #333333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Stat Cards */
@media (max-width: 768px) {
    .stat-card {
        padding: 1.5rem;
        gap: 1rem;
        height: auto;
        min-height: 100px;
    }
    
    .stat-icon {
        width: 48px;
        height: 48px;
    }
    
    .stat-icon i {
        font-size: 1.25rem;
    }
    
    .stat-content h3 {
        font-size: 2rem;
    }
    
    .stat-content p {
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .stat-card {
        padding: 1.25rem;
        gap: 0.75rem;
        flex-direction: column;
        text-align: center;
        height: auto;
    }
    
    .stat-content h3 {
        font-size: 1.75rem;
    }
}

/* Button Styles */
.btn-nd-green {
    background-color: var(--nd-green-primary);
    border-color: var(--nd-green-primary);
    color: white;
}

.btn-nd-green:hover {
    background-color: var(--nd-green-dark);
    border-color: var(--nd-green-dark);
    color: white;
}

.btn-outline-nd-green {
    color: var(--nd-green-primary);
    border-color: var(--nd-green-primary);
}

.btn-outline-nd-green:hover {
    background-color: var(--nd-green-primary);
    border-color: var(--nd-green-primary);
    color: white;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header.bg-nd-green {
    background: linear-gradient(135deg, var(--nd-green-primary) 0%, var(--nd-green-light) 100%) !important;
    border-bottom: none;
}

/* Statistics Cards */
.stat-card {
    border: none;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
}

.stat-card .card-body {
    padding: 1.5rem;
}

/* Activity Feed */
.activity-feed {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-icon i {
    color: white;
    font-size: 0.875rem;
}

.activity-content {
    flex: 1;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--nd-gray-light);
    border: none;
    font-weight: 600;
    color: var(--nd-green-primary);
    padding: 1rem 0.75rem;
}

.table td {
    border: none;
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(30, 86, 49, 0.05);
}

/* Form Styles */
.form-control:focus {
    border-color: var(--nd-green-light);
    box-shadow: 0 0 0 0.2rem rgba(30, 86, 49, 0.25);
}

.form-select:focus {
    border-color: var(--nd-green-light);
    box-shadow: 0 0 0 0.2rem rgba(30, 86, 49, 0.25);
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* Modal Styles */
.modal-header {
    background: linear-gradient(135deg, var(--nd-green-primary) 0%, var(--nd-green-light) 100%);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Search and Filter Styles */
.search-filter-bar {
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

/* Data Table Styles */
.data-table-container {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.data-table-header {
    background: linear-gradient(135deg, var(--nd-green-primary) 0%, var(--nd-green-light) 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-bottom: none;
}

.data-table-body {
    padding: 0;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Status Indicators */
.status-active {
    color: #28a745;
    font-weight: 600;
}

.status-inactive {
    color: #dc3545;
    font-weight: 600;
}

.status-pending {
    color: #ffc107;
    font-weight: 600;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-grow-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .navbar-nav .nav-link {
        font-size: 0.9rem;
        padding: 0.75rem 0.75rem;
    }
    
    .navbar-nav .nav-link i {
        font-size: 0.8rem;
    }
}

@media (max-width: 992px) {
    .navbar-nav .nav-link {
        font-size: 0.85rem;
        padding: 0.5rem 0.6rem;
    }
    
    .navbar-brand {
        font-size: 1.3rem;
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-top: 60px;
    }
    
    .navbar-nav {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
    
    .navbar-nav .nav-item {
        margin-bottom: 0.25rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 0.125rem;
    }
    
    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link.active {
        background-color: rgba(255, 255, 255, 0.15);
    }
    
    .stat-card .card-body {
        padding: 1rem;
    }
    
    .stat-card h2 {
        font-size: 1.5rem;
    }
    
    .activity-icon {
        width: 32px;
        height: 32px;
        margin-right: 0.75rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .action-buttons .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .search-filter-bar {
        padding: 1rem;
    }
    
    .data-table-header {
        padding: 0.75rem 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .page-header {
        padding: 1.5rem 0;
        margin-bottom: 1.5rem;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .search-filter-bar .row > div {
        margin-bottom: 0.75rem;
    }
    
    .search-filter-bar .row > div:last-child {
        margin-bottom: 0;
    }
}

/* Navigation Responsive Improvements */
@media (min-width: 992px) {
    .navbar-expand-lg .navbar-nav {
        flex-wrap: nowrap;
    }
    
    .navbar-expand-lg .navbar-nav .nav-item {
        white-space: nowrap;
    }
}

/* Ensure navigation items wrap properly on medium screens */
@media (max-width: 1400px) and (min-width: 992px) {
    .navbar-nav .nav-link {
        font-size: 0.875rem;
        padding: 0.75rem 0.5rem;
    }
}

/* Large screen optimization */
@media (min-width: 1400px) {
    .navbar-nav .nav-link {
        padding: 0.75rem 1.25rem;
    }
}

/* Extra small screen navigation handling */
@media (max-width: 480px) {
    .navbar-nav .nav-link {
        font-size: 0.9rem;
        padding: 0.875rem 1rem;
    }
    
    .navbar-nav .nav-link i {
        display: inline-block;
        width: 1.25rem;
        text-align: center;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

/* Smooth transitions for responsive changes */
.navbar-nav .nav-link {
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Page-specific Styles */
.page-header {
    background: linear-gradient(135deg, var(--nd-green-primary) 0%, var(--nd-green-light) 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0.5rem;
}

.page-header h1 {
    margin-bottom: 0.5rem;
}

.page-header p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Document Category Pills */
.category-pill {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--nd-green-light);
    color: white;
    border-radius: 1rem;
    font-size: 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

/* File Upload Area */
.file-upload-area {
    border: 2px dashed var(--nd-green-light);
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background-color: rgba(30, 86, 49, 0.05);
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: var(--nd-green-primary);
    background-color: rgba(30, 86, 49, 0.1);
}

.file-upload-area.dragover {
    border-color: var(--nd-green-primary);
    background-color: rgba(30, 86, 49, 0.15);
}

/* Progress Bars */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

.progress-bar {
    background: linear-gradient(90deg, var(--nd-green-primary) 0%, var(--nd-green-light) 100%);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--nd-green-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--nd-green-primary);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .action-buttons {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #e9ecef;
    }
    
    .table {
        color: #e9ecef;
    }
    
    .table th {
        background-color: #3a3a3a;
        color: var(--nd-green-light);
    }
    
    .search-filter-bar {
        background-color: #2d2d2d;
    }
    
    .data-table-container {
        background-color: #2d2d2d;
    }
} 