<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - ADAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar bg-nd-green" id="sidebar">
        <div class="sidebar-header">
            <a class="sidebar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-2"></i>ADAMS</a>
            <button class="sidebar-toggle d-lg-none" type="button" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="index.html"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="schedules.html"><i class="fas fa-calendar me-2"></i>Schedules</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#managementSubmenu" data-bs-toggle="collapse" role="button" aria-expanded="false">
                        <i class="fas fa-cogs me-2"></i>Management<i class="fas fa-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse" id="managementSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="agencies.html"><i class="fas fa-building me-2"></i>Accrediting Agencies</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="levels.html"><i class="fas fa-chart-line me-2"></i>Accreditation Levels</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="standards.html"><i class="fas fa-star me-2"></i>Quality Standards</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="departments.html"><i class="fas fa-graduation-cap me-2"></i>Programs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="documents.html"><i class="fas fa-folder me-2"></i>Documents</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="cycles.html"><i class="fas fa-sync me-2"></i>Cycle Tracker</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.html"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.html"><i class="fas fa-bell me-2"></i>Notifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="users.html"><i class="fas fa-users me-2"></i>Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.html"><i class="fas fa-cog me-2"></i>Settings</a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><span id="currentUserName">Loading...</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#"><i class="fas fa-user-edit me-1"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logoutUser()"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-mobile-toggle d-lg-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay d-lg-none" onclick="toggleSidebar()"></div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="page-header">
                <div class="container-fluid d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="fas fa-users me-3"></i>User Management</h1>
                        <p>Manage system users, roles, and permissions</p>
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="openAddUserModal()" id="addUserBtn">
                            <i class="fas fa-user-plus me-2"></i>Add User
                        </button>
                        <button class="btn btn-primary" onclick="exportUsers()">
                            <i class="fas fa-download me-2"></i>Export Users
                        </button>
                    </div>
                </div>
            </div>

            <!-- Access Control Notice -->
            <div class="row mb-4" id="accessNotice" style="display: none;">
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Access Restricted:</strong> You don't have sufficient permissions to manage users. Contact your Super Administrator.
                    </div>
                </div>
            </div>

            <!-- User Statistics -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-primary">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalUsers">0</h3>
                            <p>Total Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-success">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeUsers">0</h3>
                            <p>Active Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-warning">
                        <div class="stat-icon">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="adminUsers">0</h3>
                            <p>Admin Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card bg-info">
                        <div class="stat-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="chairUsers">0</h3>
                            <p>Area Chairs</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Management -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i>System Users</h5>
                                </div>
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <select class="form-select form-select-sm" id="roleFilter" onchange="filterUsers()">
                                                <option value="all">All Roles</option>
                                                <option value="superadmin">Super Admin</option>
                                                <option value="admin">Admin</option>
                                                <option value="areachair">Area Chair</option>
                                                <option value="subareachair">Sub-Area Chair</option>
                                                <option value="member">Member</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control form-control-sm" id="userSearch" placeholder="Search users..." onkeyup="searchUsers()">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Username</th>
                                            <th>Name</th>
                                            <th>Role</th>
                                            <th>Assigned Areas</th>
                                            <th>Status</th>
                                            <th>Last Login</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <!-- Will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-plus me-2"></i>Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Username <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="newUsername" required>
                                    <div class="form-text">Must be unique</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="newPassword" required>
                                    <div class="form-text">Minimum 6 characters</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="newFullName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="newEmail">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Role <span class="text-danger">*</span></label>
                                    <select class="form-select" id="newRole" required onchange="updateAreaAssignment()">
                                        <option value="">Select Role</option>
                                        <option value="superadmin">Super Admin</option>
                                        <option value="admin">Admin</option>
                                        <option value="areachair">Area Chair</option>
                                        <option value="subareachair">Sub-Area Chair</option>
                                        <option value="member">Member</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-select" id="newStatus">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3" id="areaAssignmentSection" style="display: none;">
                            <label class="form-label">Assigned Areas</label>
                            <div id="areaCheckboxes">
                                <!-- Will be populated by JavaScript -->
                            </div>
                            <div class="form-text">Select the accreditation areas this user will manage</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="newNotes" rows="3" placeholder="Any additional information about this user"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="createUser()">
                        <i class="fas fa-save me-1"></i>Create User
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-edit me-2"></i>Edit User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUsername">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Username</label>
                                    <input type="text" class="form-control" id="editUsernameDisplay" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="editPassword" placeholder="Leave blank to keep current password">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editFullName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="editEmail">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Role <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editRole" required onchange="updateEditAreaAssignment()">
                                        <option value="superadmin">Super Admin</option>
                                        <option value="admin">Admin</option>
                                        <option value="areachair">Area Chair</option>
                                        <option value="subareachair">Sub-Area Chair</option>
                                        <option value="member">Member</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-select" id="editStatus">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3" id="editAreaAssignmentSection" style="display: none;">
                            <label class="form-label">Assigned Areas</label>
                            <div id="editAreaCheckboxes">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="editNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateUser()">
                        <i class="fas fa-save me-1"></i>Update User
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View User Modal -->
    <div class="modal fade" id="viewUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user me-2"></i>User Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="userDetailsContent">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
    <script>
        let currentUser = null;
        let systemUsers = {};
        let currentEditingUser = null;
        
        // Quality Areas data
        const qualityAreas = [
            { id: 1, name: 'Leadership and Governance' },
            { id: 2, name: 'Quality Assurance' },
            { id: 3, name: 'Resource Management' },
            { id: 4, name: 'Teaching-Learning' },
            { id: 5, name: 'Student Services' },
            { id: 6, name: 'External Relations' },
            { id: 7, name: 'Research' },
            { id: 8, name: 'Results' }
        ];

        // Authentication check
        function checkAuthentication() {
            let user = sessionStorage.getItem('currentUser');
            if (!user) {
                // Create a mock admin user for demo purposes
                const mockUser = {
                    username: 'admin1',
                    name: 'Super Administrator',
                    role: 'superadmin',
                    email: '<EMAIL>'
                };
                sessionStorage.setItem('currentUser', JSON.stringify(mockUser));
                user = JSON.stringify(mockUser);
            }
            
            const userData = JSON.parse(user);
            
            // Check if user has permission to manage users
            if (userData.role !== 'superadmin' && userData.role !== 'admin') {
                document.getElementById('accessNotice').style.display = 'block';
                document.getElementById('addUserBtn').style.display = 'none';
                return userData; // Still return user data for basic info
            }
            
            return userData;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            currentUser = checkAuthentication();
            if (currentUser) {
                // Update user display name in navigation
                const userNameElement = document.getElementById('currentUserName');
                if (userNameElement) {
                    userNameElement.textContent = currentUser.name;
                }
                
                initializeUserManagement();
                initializeExampleData();
            }
        });

        function initializeUserManagement() {
            // Load system users from localStorage or initialize with defaults
            const storedUsers = getSystemUsers();
            if (Object.keys(storedUsers).length === 0) {
                // Initialize with default users if localStorage is empty
                systemUsers = {
                    'admin1': { password: 'hazel', role: 'superadmin', name: 'Super Administrator', areas: 'all', status: 'active', email: '<EMAIL>', lastLogin: '2024-12-15 14:30', notes: 'System Super Administrator' },
                    'admin': { password: 'admin', role: 'admin', name: 'System Administrator', areas: 'all', status: 'active', email: '<EMAIL>', lastLogin: '2024-12-15 10:15', notes: 'Primary System Administrator' },
                    'chair1': { password: 'admin', role: 'areachair', name: 'Area Chair 1', areas: [1, 2], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-15 09:45', notes: 'Manages Leadership & Quality Assurance areas' },
                    'chair2': { password: 'admin', role: 'areachair', name: 'Area Chair 2', areas: [3, 4], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-14 16:20', notes: 'Manages Resource Management & Teaching-Learning areas' },
                    'chair3': { password: 'admin', role: 'areachair', name: 'Area Chair 3', areas: [5, 6], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-14 11:10', notes: 'Manages Student Services & External Relations areas' },
                    'chair4': { password: 'admin', role: 'areachair', name: 'Area Chair 4', areas: [7, 8], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-13 15:30', notes: 'Manages Research & Results areas' },
                    'subchair1': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 1', areas: [1], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-15 08:20', notes: 'Manages Leadership and Governance sub-area' },
                    'subchair2': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 2', areas: [2], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-14 13:45', notes: 'Manages Quality Assurance sub-area' },
                    'subchair3': { password: 'admin', role: 'subareachair', name: 'Sub-Area Chair 3', areas: [3], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-13 10:15', notes: 'Manages Resource Management sub-area' },
                    'member1': { password: 'admin', role: 'member', name: 'Faculty Member 1', areas: [], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-15 12:30', notes: 'Faculty member - Computer Science Department' },
                    'member2': { password: 'admin', role: 'member', name: 'Faculty Member 2', areas: [], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-14 14:20', notes: 'Faculty member - Business Administration Department' },
                    'member3': { password: 'admin', role: 'member', name: 'Faculty Member 3', areas: [], status: 'active', email: '<EMAIL>', lastLogin: '2024-12-13 16:45', notes: 'Faculty member - Education Department' }
                };
                saveSystemUsers(systemUsers);
            } else {
                systemUsers = storedUsers;
            }
            
            loadUsers();
            loadStatistics();
            setupAreaCheckboxes();
        }

        function loadUsers() {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            // Add a summary row with total user count
            const total = Object.keys(systemUsers).length;
            const summaryRow = `<tr class="table-secondary"><td colspan="7" class="text-center font-weight-bold">Total Users: ${total}</td></tr>`;
            tbody.insertAdjacentHTML('beforeend', summaryRow);

            Object.entries(systemUsers).forEach(([username, userData]) => {
                const areasText = getAreasText(userData.areas);
                const row = `
                    <tr>
                        <td><strong>${username}</strong></td>
                        <td>${userData.name}</td>
                        <td><span class="badge ${getRoleBadgeClass(userData.role)}">${formatRole(userData.role)}</span></td>
                        <td>${areasText}</td>
                        <td>${userData.status}</td>
                        <td>${userData.lastLogin || 'Never'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewUser('${username}')" title="View"><i class="fas fa-eye"></i></button>
                            ${currentUser.role === 'superadmin' || (currentUser.role === 'admin' && userData.role !== 'superadmin') ? `
                                <button class="btn btn-sm btn-outline-warning me-1" onclick="editUser('${username}')" title="Edit"><i class="fas fa-edit"></i></button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${username}')" title="Delete"><i class="fas fa-trash"></i></button>
                            ` : ''}
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        function loadStatistics() {
            // Fetch statistics from the server (stats.json) using AJAX (Fetch API)
            fetch('stats.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Update the dashboard cards with the fetched data
                    document.getElementById('totalUsers').textContent = data.totalUsers ?? 0;
                    document.getElementById('activeUsers').textContent = data.activeUsers ?? 0;
                    document.getElementById('adminUsers').textContent = data.adminUsers ?? 0;
                    document.getElementById('chairUsers').textContent = data.chairUsers ?? 0;
                })
                .catch(error => {
                    console.error('Failed to load statistics:', error);
                    // Fallback: clear values or keep previous content
                });
        }

        function setupAreaCheckboxes() {
            const addContainer = document.getElementById('areaCheckboxes');
            const editContainer = document.getElementById('editAreaCheckboxes');
            
            const checkboxesHtml = qualityAreas.map(area => `
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="checkbox" id="area${area.id}" value="${area.id}">
                    <label class="form-check-label" for="area${area.id}">Area ${area.id}</label>
                </div>
            `).join('');
            
            addContainer.innerHTML = checkboxesHtml;
            editContainer.innerHTML = checkboxesHtml.replace(/area(\d+)/g, 'editArea$1');
        }

        function updateAreaAssignment() {
            const role = document.getElementById('newRole').value;
            const section = document.getElementById('areaAssignmentSection');
            
            if (role === 'areachair' || role === 'subareachair') {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        }

        function updateEditAreaAssignment() {
            const role = document.getElementById('editRole').value;
            const section = document.getElementById('editAreaAssignmentSection');
            
            if (role === 'areachair' || role === 'subareachair') {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        }

        function openAddUserModal() {
            if (currentUser.role !== 'superadmin' && currentUser.role !== 'admin') {
                showToast('Only Administrators and Super Administrators can add new users', 'warning');
                return;
            }
            
            const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
            modal.show();
        }

        function createUser() {
            if (!validateForm('addUserForm')) return;

            const username = document.getElementById('newUsername').value;
            const password = document.getElementById('newPassword').value;
            const fullName = document.getElementById('newFullName').value;
            const email = document.getElementById('newEmail').value;
            const role = document.getElementById('newRole').value;
            const status = document.getElementById('newStatus').value;
            const notes = document.getElementById('newNotes').value;

            // Check if username already exists
            if (systemUsers[username]) {
                showToast('Username already exists. Please choose a different username.', 'error');
                return;
            }

            // Check if admin is trying to create a superadmin (only superadmin can create superadmin)
            if (currentUser.role === 'admin' && role === 'superadmin') {
                showToast('Only Super Administrators can create Super Administrator accounts', 'warning');
                return;
            }

            // Get assigned areas
            let areas = [];
            if (role === 'areachair' || role === 'subareachair') {
                const checkboxes = document.querySelectorAll('#areaCheckboxes input[type="checkbox"]:checked');
                areas = Array.from(checkboxes).map(cb => parseInt(cb.value));
                
                if (areas.length === 0) {
                    showToast('Please assign at least one area for this role', 'warning');
                    return;
                }
            } else if (role === 'superadmin' || role === 'admin') {
                areas = 'all';
            }

            // Create new user
            systemUsers[username] = {
                password: password,
                role: role,
                name: fullName,
                email: email,
                areas: areas,
                status: status,
                lastLogin: null,
                notes: notes,
                createdBy: currentUser.username || currentUser.name,
                createdDate: new Date().toISOString()
            };

            // Save to localStorage
            saveSystemUsers(systemUsers);

            loadUsers();
            loadStatistics();
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
            modal.hide();
            
            // Reset form
            document.getElementById('addUserForm').reset();
            document.getElementById('areaAssignmentSection').style.display = 'none';
            
            showToast(`User "${username}" created successfully!`, 'success');
        }

        function viewUser(username) {
            const user = systemUsers[username];
            if (!user) return;

            const areasText = getAreasText(user.areas);
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">User Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Username:</strong></td><td>${username}</td></tr>
                            <tr><td><strong>Full Name:</strong></td><td>${user.name}</td></tr>
                            <tr><td><strong>Email:</strong></td><td>${user.email || 'Not provided'}</td></tr>
                            <tr><td><strong>Role:</strong></td><td><span class="badge ${getRoleBadgeClass(user.role)}">${formatRole(user.role)}</span></td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge ${user.status === 'active' ? 'bg-success' : 'bg-secondary'}">${user.status}</span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Access & Permissions</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Assigned Areas:</strong></td><td>${areasText}</td></tr>
                            <tr><td><strong>Last Login:</strong></td><td>${user.lastLogin || 'Never'}</td></tr>
                            <tr><td><strong>Created:</strong></td><td>${user.createdDate ? new Date(user.createdDate).toLocaleDateString() : 'System user'}</td></tr>
                            <tr><td><strong>Created By:</strong></td><td>${user.createdBy || 'System'}</td></tr>
                        </table>
                    </div>
                </div>
                ${user.notes ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Notes</h6>
                            <p>${user.notes}</p>
                        </div>
                    </div>
                ` : ''}
            `;
            
            document.getElementById('userDetailsContent').innerHTML = detailsHtml;
            const modal = new bootstrap.Modal(document.getElementById('viewUserModal'));
            modal.show();
        }

        function editUser(username) {
            if (currentUser.role !== 'superadmin' && currentUser.role !== 'admin') {
                showToast('Only Administrators and Super Administrators can edit users', 'warning');
                return;
            }

            const user = systemUsers[username];
            if (!user) return;

            // Prevent admin from editing superadmin accounts
            if (currentUser.role === 'admin' && user.role === 'superadmin') {
                showToast('Only Super Administrators can edit Super Administrator accounts', 'warning');
                return;
            }

            currentEditingUser = username;

            // Populate form
            document.getElementById('editUsername').value = username;
            document.getElementById('editUsernameDisplay').value = username;
            document.getElementById('editFullName').value = user.name;
            document.getElementById('editEmail').value = user.email || '';
            document.getElementById('editRole').value = user.role;
            document.getElementById('editStatus').value = user.status;
            document.getElementById('editNotes').value = user.notes || '';

            // Handle area assignments
            updateEditAreaAssignment();
            
            if (Array.isArray(user.areas)) {
                // Clear all checkboxes first
                document.querySelectorAll('#editAreaCheckboxes input[type="checkbox"]').forEach(cb => cb.checked = false);
                
                // Check assigned areas
                user.areas.forEach(areaId => {
                    const checkbox = document.getElementById(`editArea${areaId}`);
                    if (checkbox) checkbox.checked = true;
                });
            }

            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        }

        function updateUser() {
            if (!validateForm('editUserForm')) return;

            const username = currentEditingUser;
            const user = systemUsers[username];
            
            const newPassword = document.getElementById('editPassword').value;
            const fullName = document.getElementById('editFullName').value;
            const email = document.getElementById('editEmail').value;
            const role = document.getElementById('editRole').value;
            const status = document.getElementById('editStatus').value;
            const notes = document.getElementById('editNotes').value;

            // Prevent admin from changing role to superadmin
            if (currentUser.role === 'admin' && role === 'superadmin') {
                showToast('Only Super Administrators can create Super Administrator accounts', 'warning');
                return;
            }

            // Prevent admin from editing superadmin user role
            if (currentUser.role === 'admin' && user.role === 'superadmin') {
                showToast('Only Super Administrators can modify Super Administrator accounts', 'warning');
                return;
            }

            // Get assigned areas
            let areas = user.areas; // Keep existing if not area-based role
            if (role === 'areachair' || role === 'subareachair') {
                const checkboxes = document.querySelectorAll('#editAreaCheckboxes input[type="checkbox"]:checked');
                areas = Array.from(checkboxes).map(cb => parseInt(cb.value));
                
                if (areas.length === 0) {
                    showToast('Please assign at least one area for this role', 'warning');
                    return;
                }
            } else if (role === 'superadmin' || role === 'admin') {
                areas = 'all';
            } else {
                areas = [];
            }

            // Update user
            systemUsers[username] = {
                ...user,
                password: newPassword || user.password, // Keep existing if not changed
                name: fullName,
                email: email,
                role: role,
                areas: areas,
                status: status,
                notes: notes,
                lastModified: new Date().toISOString(),
                modifiedBy: currentUser.username || currentUser.name
            };

            // Save to localStorage
            saveSystemUsers(systemUsers);

            loadUsers();
            loadStatistics();
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
            modal.hide();
            
            showToast(`User "${username}" updated successfully!`, 'success');
        }

        function deleteUser(username) {
            if (currentUser.role !== 'superadmin' && currentUser.role !== 'admin') {
                showToast('Only Administrators and Super Administrators can delete users', 'warning');
                return;
            }

            const user = systemUsers[username];
            if (!user) return;

            // Prevent admin from deleting superadmin accounts
            if (currentUser.role === 'admin' && user.role === 'superadmin') {
                showToast('Only Super Administrators can delete Super Administrator accounts', 'warning');
                return;
            }

            // Prevent deleting yourself
            if (username === currentUser.username) {
                showToast('You cannot delete your own account', 'warning');
                return;
            }

            // Prevent deleting the primary super admin
            if (username === 'admin1') {
                showToast('Cannot delete the primary Super Administrator account', 'warning');
                return;
            }

            if (confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
                delete systemUsers[username];
                
                // Save to localStorage
                saveSystemUsers(systemUsers);
                
                loadUsers();
                loadStatistics();
                showToast(`User "${username}" deleted successfully!`, 'success');
            }
        }

        function exportUsers() {
            const csvData = `User Export - ${new Date().toLocaleString()}

Username,Name,Role,Email,Status,Assigned Areas,Last Login,Notes
${Object.entries(systemUsers).map(([username, user]) => {
                const areasText = getAreasText(user.areas);
                return `"${username}","${user.name}","${formatRole(user.role)}","${user.email || ''}","${user.status}","${areasText}","${user.lastLogin || 'Never'}","${user.notes || ''}"`;
            }).join('\n')}`;
            
            downloadCSV(csvData, `ADAMS_Users_Export_${new Date().toISOString().split('T')[0]}.csv`);
            showToast('Users exported successfully!', 'success');
        }

        function filterUsers() {
            const filter = document.getElementById('roleFilter').value;
            const rows = document.querySelectorAll('#usersTableBody tr');
            
            rows.forEach(row => {
                if (filter === 'all') {
                    row.style.display = '';
                } else {
                    const roleCell = row.children[2];
                    const roleText = roleCell ? roleCell.textContent.toLowerCase() : '';
                    const showRow = roleText.includes(formatRole(filter).toLowerCase());
                    row.style.display = showRow ? '' : 'none';
                }
            });
        }

        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const rows = document.querySelectorAll('#usersTableBody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }

        // Helper functions
        function getRoleBadgeClass(role) {
            switch(role) {
                case 'superadmin': return 'bg-warning text-dark';
                case 'admin': return 'bg-primary';
                case 'areachair': return 'bg-success';
                case 'subareachair': return 'bg-info';
                case 'member': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        function formatRole(role) {
            switch(role) {
                case 'superadmin': return 'Super Administrator';
                case 'admin': return 'Administrator';
                case 'areachair': return 'Area Chair';
                case 'subareachair': return 'Sub-Area Chair';
                case 'member': return 'Member';
                default: return 'Unknown';
            }
        }

        function getAreasText(areas) {
            if (areas === 'all') {
                return 'All Areas (Full Access)';
            } else if (Array.isArray(areas) && areas.length > 0) {
                return `Areas: ${areas.join(', ')}`;
            } else {
                return 'No specific areas';
            }
        }

        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // Helper functions for localStorage management
        function getSystemUsers() {
            return JSON.parse(localStorage.getItem('adamsSystemUsers') || '{}');
        }

        function saveSystemUsers(users) {
            localStorage.setItem('adamsSystemUsers', JSON.stringify(users));
        }

        // Utility Functions
        function showToast(message, type = 'success') {
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '1056';
                document.body.appendChild(toastContainer);
            }

            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'error' ? 'danger' : type;
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-bg-${bgClass}" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (toastElement) {
                    toastElement.remove();
                }
            }, 5000);
        }

        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;
            
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                // Remove existing error styling
                field.classList.remove('is-invalid');
                const errorElement = field.parentNode.querySelector('.invalid-feedback');
                if (errorElement) {
                    errorElement.remove();
                }

                // Check if field is empty
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                    
                    // Add error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'invalid-feedback';
                    errorDiv.textContent = 'This field is required.';
                    field.parentNode.appendChild(errorDiv);
                } else {
                    field.classList.add('is-valid');
                }
            });

            // Additional validation for password length
            const passwordField = form.querySelector('input[type="password"][required]');
            if (passwordField && passwordField.value && passwordField.value.length < 6) {
                isValid = false;
                passwordField.classList.add('is-invalid');
                
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = 'Password must be at least 6 characters long.';
                passwordField.parentNode.appendChild(errorDiv);
            }

            return isValid;
        }

        // Logout function
        function logoutUser() {
            if (confirm('Are you sure you want to logout?')) {
                sessionStorage.removeItem('currentUser');
                showToast('Logged out successfully!', 'success');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
            }
        }

        // Additional utility functions for better user management
        function refreshUserData() {
            // Sync with localStorage to get latest user data
            systemUsers = getSystemUsers();
            loadUsers();
            loadStatistics();
        }

        function resetAddUserForm() {
            const form = document.getElementById('addUserForm');
            if (form) {
                form.reset();
                
                // Clear validation styling
                form.querySelectorAll('.is-valid, .is-invalid').forEach(field => {
                    field.classList.remove('is-valid', 'is-invalid');
                });
                
                // Remove error messages
                form.querySelectorAll('.invalid-feedback').forEach(error => {
                    error.remove();
                });
                
                // Hide area assignment section
                document.getElementById('areaAssignmentSection').style.display = 'none';
            }
        }

        function resetEditUserForm() {
            const form = document.getElementById('editUserForm');
            if (form) {
                form.reset();
                
                // Clear validation styling
                form.querySelectorAll('.is-valid, .is-invalid').forEach(field => {
                    field.classList.remove('is-valid', 'is-invalid');
                });
                
                // Remove error messages
                form.querySelectorAll('.invalid-feedback').forEach(error => {
                    error.remove();
                });
                
                // Hide area assignment section
                document.getElementById('editAreaAssignmentSection').style.display = 'none';
            }
        }

        // Enhanced form handling
        function handleModalClose(modalId) {
            if (modalId === 'addUserModal') {
                resetAddUserForm();
            } else if (modalId === 'editUserModal') {
                resetEditUserForm();
            }
        }

        // Setup modal event listeners for proper cleanup
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for modal close events
            const addModal = document.getElementById('addUserModal');
            const editModal = document.getElementById('editUserModal');
            
            if (addModal) {
                addModal.addEventListener('hidden.bs.modal', () => handleModalClose('addUserModal'));
            }
            
            if (editModal) {
                editModal.addEventListener('hidden.bs.modal', () => handleModalClose('editUserModal'));
            }
        });

        function initializeExampleData() {
            // Add comprehensive example data to localStorage
            const existingData = JSON.parse(localStorage.getItem('adamsSystemData') || '{}');
            
            if (!existingData.programs || existingData.programs.length === 0) {
                const sampleData = {
                    programs: [
                        {
                            id: 1,
                            name: 'Bachelor of Science in Computer Science',
                            department: 'College of Information Technology and Engineering (CITE)',
                            level: 'Level II',
                            status: 'Active',
                            accreditor: 'AACCUP',
                            nextVisit: '2025-03-15',
                            description: 'A comprehensive computer science program focusing on software development, algorithms, and system design.',
                            headCount: 245,
                            faculty: 18,
                            lastAccredited: '2022-03-15'
                        },
                        {
                            id: 2,
                            name: 'Bachelor of Science in Information Technology',
                            department: 'College of Information Technology and Engineering (CITE)',
                            level: 'Level I',
                            status: 'Active',
                            accreditor: 'AACCUP',
                            nextVisit: '2024-12-20',
                            description: 'Focuses on practical applications of technology in business environments.',
                            headCount: 189,
                            faculty: 15,
                            lastAccredited: '2021-12-20'
                        },
                        {
                            id: 3,
                            name: 'Bachelor of Science in Business Administration',
                            department: 'College of Business Administration (CBA)',
                            level: 'Level III',
                            status: 'Active',
                            accreditor: 'AACCUP',
                            nextVisit: '2025-06-10',
                            description: 'Comprehensive business education covering management, marketing, and finance.',
                            headCount: 312,
                            faculty: 22,
                            lastAccredited: '2022-06-10'
                        },
                        {
                            id: 4,
                            name: 'Bachelor of Elementary Education',
                            department: 'College of Education (COE)',
                            level: 'Level II',
                            status: 'Active',
                            accreditor: 'AACCUP',
                            nextVisit: '2025-01-25',
                            description: 'Prepares students to become effective elementary school teachers.',
                            headCount: 156,
                            faculty: 14,
                            lastAccredited: '2022-01-25'
                        },
                        {
                            id: 5,
                            name: 'Bachelor of Science in Nursing',
                            department: 'College of Nursing (CON)',
                            level: 'Level I',
                            status: 'Active',
                            accreditor: 'CHED',
                            nextVisit: '2025-04-30',
                            description: 'Comprehensive nursing program with clinical training and theoretical foundation.',
                            headCount: 198,
                            faculty: 16,
                            lastAccredited: '2022-04-30'
                        }
                    ],
                    documents: [
                        {
                            id: 1,
                            title: 'Strategic Plan 2024-2029',
                            category: 'Planning Documents',
                            area: 1,
                            status: 'Under Review',
                            uploadedBy: 'chair1',
                            uploadDate: '2024-12-10',
                            reviewDate: '2024-12-20',
                            version: '2.1',
                            fileSize: '2.4 MB',
                            description: 'Five-year strategic plan outlining institutional goals and objectives.'
                        },
                        {
                            id: 2,
                            title: 'Faculty Manual 2024',
                            category: 'Policy Documents',
                            area: 2,
                            status: 'Approved',
                            uploadedBy: 'admin',
                            uploadDate: '2024-11-15',
                            reviewDate: '2024-11-20',
                            version: '3.0',
                            fileSize: '5.2 MB',
                            description: 'Updated faculty policies and procedures manual.'
                        },
                        {
                            id: 3,
                            title: 'Budget Report 2024',
                            category: 'Financial Documents',
                            area: 3,
                            status: 'Approved',
                            uploadedBy: 'chair2',
                            uploadDate: '2024-12-01',
                            reviewDate: '2024-12-05',
                            version: '1.0',
                            fileSize: '1.8 MB',
                            description: 'Annual budget allocation and expenditure report.'
                        },
                        {
                            id: 4,
                            title: 'Curriculum Guide - CS Program',
                            category: 'Academic Documents',
                            area: 4,
                            status: 'Under Review',
                            uploadedBy: 'subchair1',
                            uploadDate: '2024-12-08',
                            reviewDate: '2024-12-18',
                            version: '4.2',
                            fileSize: '3.1 MB',
                            description: 'Updated curriculum for Computer Science program.'
                        },
                        {
                            id: 5,
                            title: 'Student Services Manual',
                            category: 'Student Documents',
                            area: 5,
                            status: 'Draft',
                            uploadedBy: 'chair3',
                            uploadDate: '2024-12-12',
                            reviewDate: '2024-12-22',
                            version: '1.5',
                            fileSize: '2.7 MB',
                            description: 'Comprehensive guide to student services and support systems.'
                        }
                    ],
                    cycles: [
                        {
                            id: 1,
                            program: 'BS Computer Science',
                            accreditor: 'AACCUP',
                            level: 'Level II',
                            startDate: '2022-03-15',
                            endDate: '2025-03-15',
                            status: 'Active',
                            progress: 75,
                            nextMilestone: 'Mid-term Report',
                            milestoneDate: '2024-12-30'
                        },
                        {
                            id: 2,
                            program: 'BS Information Technology',
                            accreditor: 'AACCUP',
                            level: 'Level I',
                            startDate: '2021-12-20',
                            endDate: '2024-12-20',
                            status: 'Due for Renewal',
                            progress: 95,
                            nextMilestone: 'Accreditation Visit',
                            milestoneDate: '2024-12-20'
                        },
                        {
                            id: 3,
                            program: 'BS Business Administration',
                            accreditor: 'AACCUP',
                            level: 'Level III',
                            startDate: '2022-06-10',
                            endDate: '2025-06-10',
                            status: 'Active',
                            progress: 60,
                            nextMilestone: 'Documentation Review',
                            milestoneDate: '2025-01-15'
                        }
                    ],
                    notifications: [
                        {
                            id: 1,
                            title: 'Document Review Required',
                            message: 'Strategic Plan 2024 requires your review and approval',
                            type: 'warning',
                            read: false,
                            date: '2024-12-15',
                            recipient: 'chair1',
                            priority: 'high'
                        },
                        {
                            id: 2,
                            title: 'Accreditation Visit Scheduled',
                            message: 'CITE programs accreditation visit scheduled for March 15, 2025',
                            type: 'info',
                            read: false,
                            date: '2024-12-14',
                            recipient: 'all',
                            priority: 'medium'
                        },
                        {
                            id: 3,
                            title: 'Quality Standards Updated',
                            message: 'New AACCUP quality standards have been published and are now available',
                            type: 'success',
                            read: true,
                            date: '2024-12-13',
                            recipient: 'all',
                            priority: 'low'
                        }
                    ],
                    reports: [
                        {
                            id: 1,
                            title: 'Annual Accreditation Status Report',
                            type: 'Status Report',
                            date: '2024-12-01',
                            generatedBy: 'admin1',
                            status: 'Completed',
                            programs: ['BS Computer Science', 'BS Information Technology', 'BS Business Administration'],
                            format: 'PDF'
                        },
                        {
                            id: 2,
                            title: 'Faculty Compliance Report',
                            type: 'Compliance Report',
                            date: '2024-11-25',
                            generatedBy: 'admin',
                            status: 'Completed',
                            programs: ['All Programs'],
                            format: 'Excel'
                        }
                    ],
                    standards: [
                        {
                            id: 1,
                            area: 1,
                            title: 'Institutional Leadership',
                            code: 'LG-001',
                            description: 'The institution demonstrates effective leadership and governance structures',
                            compliance: 'Compliant',
                            lastReview: '2024-11-15'
                        },
                        {
                            id: 2,
                            area: 2,
                            title: 'Quality Assurance System',
                            code: 'QA-001',
                            description: 'Institution has established comprehensive quality assurance mechanisms',
                            compliance: 'Partially Compliant',
                            lastReview: '2024-11-20'
                        }
                    ]
                };
                
                localStorage.setItem('adamsSystemData', JSON.stringify(sampleData));
            }
        }
    </script>
</body>
</html> 